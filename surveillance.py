#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# douyin-surveillance-bot.py - 持续监控抖音作者作品更新，且只允许存非空数据，不用空值覆盖数据库

import asyncio
import json
import logging
import os
import re
import shutil
import subprocess
import time
import uuid
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
import random

import httpx
from pyrogram import Client
from pyrogram.types import InputMediaPhoto, InputMediaVideo, InputMediaDocument, InputMediaAudio
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from pyrogram.enums import ParseMode
from pyrogram.errors import FloodWait
from supabase import create_client, Client as SupabaseClient
import io
from dotenv import load_dotenv
from PIL import Image
import pillow_heif

# .env 文件路径
ENV_PATH = '/home/<USER>/DLR/DLR/.env'

# =============== 加载配置 ===============
# TikHub API 基础 URL (固定)
TIKHUB_API_BASE_URL = "https://beta.tikhub.io/api/v1/douyin/web"

# =============== 日志配置 ===============
logger = logging.getLogger("douyin-surveillance")
logger.setLevel(logging.INFO)

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

# 将处理器添加到日志记录器
logger.addHandler(console_handler)

# 创建文件处理器
file_handler = logging.FileHandler('douyin-surveillance.log')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# 加载环境变量并检查必需的配置
logger.info(f"正在从 {ENV_PATH} 加载环境变量...")
dotenv_loaded = load_dotenv(dotenv_path=ENV_PATH)

required_env_vars = [
    'API_ID', 'API_HASH', 'BOT_TOKEN',
    'SUPABASE_URL', 'SUPABASE_KEY', 'TELEGRAM_CHAT_ID',
    'TIKHUB_API_TOKEN', 'hulu_COOKIE', '500_COOKIE',
    'LOCAL_API_CONTAINER_NAME'
]
config = {}
missing_vars = []

if not dotenv_loaded:
    logger.error(f"无法加载 .env 文件: {ENV_PATH}")

for var in required_env_vars:
    value = os.getenv(var)
    if value:
        config[var] = value
        logger.info(f"成功加载环境变量: {var}")
    else:
        missing_vars.append(var)
        logger.error(f"必需的环境变量缺失: {var}")

if missing_vars:
    raise ValueError(f"启动失败，缺少以下环境变量: {', '.join(missing_vars)}")

# 将加载的配置赋值给常量 (虽然不是真正的常量，但遵循惯例)
API_ID = config['API_ID']
API_HASH = config['API_HASH']
BOT_TOKEN = config['BOT_TOKEN']
SUPABASE_URL = config['SUPABASE_URL']
SUPABASE_KEY = config['SUPABASE_KEY']
TELEGRAM_CHAT_ID = int(config['TELEGRAM_CHAT_ID'])  # 转换为整数
TIKHUB_API_TOKEN = config['TIKHUB_API_TOKEN']
HULU_COOKIE = config['hulu_COOKIE']
FIVE00_COOKIE = config['500_COOKIE']
LOCAL_API_CONTAINER_NAME = config['LOCAL_API_CONTAINER_NAME']

# 其他配置 (可考虑也移入 .env)
API_BASE_URL = os.getenv('API_BASE_URL', "http://localhost:80")  # 本地API可选，带默认值
DELETE_FILES = os.getenv('DELETE_FILES', 'True').lower() == 'true'
SCAN_INTERVAL = int(os.getenv('SCAN_INTERVAL', '0'))
PROCESS_DELAY = int(os.getenv('PROCESS_DELAY', '0'))
VIDEO_THUMBNAIL_THRESHOLD = int(os.getenv('VIDEO_THUMBNAIL_THRESHOLD', '9'))

# 点赞内容查询页数配置（非全量扫描时）
LIKED_VIDEOS_PAGES = int(os.getenv('LIKED_VIDEOS_PAGES', '1'))  # 默认查询前3页

# 创建 Supabase 客户端
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

# 全局变量
bot_username = ""
primary_bot = None

# =============== 辅助函数：合并新旧字段，跳过空值 ===============
def sanitize_and_merge(existing_row: dict, new_data: dict) -> dict:
    """
    将 new_data 中的有效字段覆盖 existing_row。
    规则：
    1. 如果 new_data 中的字段值为 None，则不使用 None 覆盖 existing_row 中的已有值，保留旧值。
    2. 如果 new_data 中的字符串类型字段值为空字符串 (例如 ""，且非JSON结构的空串如 "{}")，
       则不使用空字符串覆盖 existing_row 中的已有值，保留旧值。
    3. 如果 new_data 中不包含某个字段，existing_row 中该字段的值自然保留。
    4. live_status 字段在进入此函数前已被处理为布尔值，此处的通用规则同样适用。
       例如，如果 new_data['live_status'] 是 False，它会覆盖旧值。
    """
    if existing_row is None:
        existing_row = {}
    
    # merged 初始化为 existing_row 的副本。
    # 这样，如果后续我们因为新数据是 None 或空字符串而选择"跳过"更新，
    # merged 中自然会保留 existing_row 的原始值。
    merged = dict(existing_row)

    for key, new_value in new_data.items():
        # Special handling for 'new_url':
        # If 'new_url' already exists and has a value in the database (merged from existing_row),
        # then DO NOT update it from new_data, regardless of new_value.
        if key == "new_url":
            # Check if existing_row (via merged) already has a valid new_url
            # A "valid" existing new_url is one that's not None and not an empty string.
            existing_new_url = merged.get(key)
            if existing_new_url and isinstance(existing_new_url, str) and existing_new_url.strip():
                logger.info(f"Field 'new_url' ({key}) already exists in DB ('{existing_new_url}'), preserving it and skipping update from new_data which was ('{new_value}').")
                continue # Skip updating new_url, keep the old one

        # 规则1: 如果新值为 None，则不更新，保留 merged 中已有的值 (来自 existing_row)。
        if new_value is None:
            continue

        # 规则2: 如果新值是字符串类型，则进一步检查。
        if isinstance(new_value, str):
            # 如果是 JSON 格式的字符串 (例如 "{}", "[]")，则允许更新。
            if new_value.strip().startswith(("{", "[")):
                merged[key] = new_value
                continue
            # 否则，如果是普通的空字符串，则不更新，保留 merged 中已有的值。
            if not new_value.strip():
                continue
        
        # 对于其他所有情况 (例如，new_value 不是 None，也不是需要跳过的空字符串，
        # 或者 new_value 是其他数据类型且有有效值，
        # 或者它是通过了上述检查的 JSON 字符串)，
        # 则使用 new_value 更新 merged 中的对应字段。
        merged[key] = new_value
        
    return merged

# =============== 抖音解析器 ===============
class DouyinParser:
    @staticmethod
    def _sort_bitrate_items(bit_rate_list):
        def sort_key(item):
            play_addr = item.get('play_addr', {})
            width = play_addr.get('width', 0)
            height = play_addr.get('height', 0)
            resolution = width * height
            fps = item.get('FPS', 0)
            bitrate = item.get('bit_rate', 0)
            return (-resolution, -fps, -bitrate)
        return sorted(bit_rate_list, key=sort_key)

    @staticmethod
    def _get_first_url(url_data):
        url_list = url_data.get('url_list', []) if url_data else []
        if url_list and isinstance(url_list, list) and len(url_list) > 0:
            return url_list[0]
        return ""
    
    @staticmethod
    def _get_backup_urls(url_data, max_backup=2):
        """获取备用URL列表（最多max_backup个）"""
        url_list = url_data.get('url_list', []) if url_data else []
        if not url_list or not isinstance(url_list, list) or len(url_list) <= 1:
            return []
        # 返回从第二个URL开始，最多max_backup个
        return url_list[1:min(len(url_list), 1+max_backup)]

    @staticmethod
    def _get_best_play_url(video_info, return_backups=False):
        """
        获取最佳播放URL
        Args:
            video_info: 视频信息字典
            return_backups: 是否返回备用链接
        
        Returns:
            如果return_backups=False，返回最佳URL字符串
            如果return_backups=True，返回(最佳URL, [备用URL1, 备用URL2, ...])
        """
        bit_rate_list = video_info.get('bit_rate', []) if video_info else []
        best_url = ""
        backup_urls = []
        
        if bit_rate_list:
            sorted_items = DouyinParser._sort_bitrate_items(bit_rate_list)
            
            # 找到最佳URL
            for item in sorted_items:
                play_addr = item.get('play_addr', {})
                url = DouyinParser._get_first_url(play_addr)
                if url and "watermark" not in url:
                    best_url = url
                    if return_backups:
                        # 收集该条目的所有备用URL
                        backup_urls.extend(DouyinParser._get_backup_urls(play_addr))
                    break
            
            # 如果没找到无水印URL，尝试寻找其他码率的URL作为备用
            if return_backups and backup_urls == [] and len(sorted_items) > 1:
                for item in sorted_items[1:]:  # 从第二个开始
                    play_addr = item.get('play_addr', {})
                    url = DouyinParser._get_first_url(play_addr)
                    if url and url != best_url and "watermark" not in url:
                        backup_urls.append(url)
                        if len(backup_urls) >= 2:  # 最多2个备用
                            break

        # 如果bit_rate_list没有找到合适的，尝试使用默认play_addr
        if not best_url:
            play_addr = video_info.get('play_addr', {})
            best_url = DouyinParser._get_first_url(play_addr)
            if return_backups:
                backup_urls = DouyinParser._get_backup_urls(play_addr)
                
        # 确保无水印URL（如果有的话）
        fallback_url = best_url
        if fallback_url and "watermark" not in fallback_url:
            best_url = fallback_url
        
        if return_backups:
            # 最多返回2个备用URL
            return best_url, backup_urls[:2]
        return best_url

    @staticmethod
    def _get_best_image_url(img_data):
        url_list = img_data.get('url_list', []) if img_data else []
        no_water_urls = [u for u in url_list if "water" not in u]
        if no_water_urls:
            return no_water_urls[0]
        if url_list:
            return url_list[0]
        return ""

    @staticmethod
    def parse_aweme(resp_data):
        try:
            data = resp_data.get('data', {}).get('aweme_detail', {}) if resp_data else {}
            if not isinstance(data, dict):
                return {}

            aweme_id = data.get('aweme_id', "")
            desc = data.get('desc', "")
            create_time_ts = data.get('create_time', 0)
            create_time_str = ""
            try:
                # 转换为北京时间（UTC+8）
                from datetime import timezone, timedelta
                beijing_tz = timezone(timedelta(hours=8))
                # 先创建UTC时间，然后转换为北京时间
                utc_time = datetime.fromtimestamp(create_time_ts, tz=timezone.utc)
                beijing_time = utc_time.astimezone(beijing_tz)
                create_time_str = beijing_time.strftime('%Y-%m-%d %H:%M:%S')
            except Exception:
                pass

            author = data.get('author', {})
            author_info = {
                'nickname': author.get('nickname', ""),
                'uid': author.get('uid', ""),
                'sec_uid': author.get('sec_uid', ""),
                'unique_id': author.get('unique_id', ""),
                'follower_count': author.get('follower_count', 0),
                'total_favorited': author.get('total_favorited', 0),
            }

            location_info = {}
            anchor_info = data.get('anchor_info', {})
            try:
                if isinstance(anchor_info.get('extra'), str):
                    extra_data = json.loads(anchor_info.get('extra'))
                    if isinstance(extra_data, dict):
                        address_info = extra_data.get('address_info', {})
                        location_info = {
                            'province': address_info.get('province', ""),
                            'city': address_info.get('city', "")
                        }
            except Exception:
                pass

            statistics = data.get('statistics', {})
            stats = {
                'comment_count': statistics.get('comment_count', 0),
                'digg_count': statistics.get('digg_count', 0),
                'collect_count': statistics.get('collect_count', 0),
                'share_count': statistics.get('share_count', 0)
            }

            music = data.get('music', {})
            music_info = {
                'title': music.get('title', ""),
                'author': music.get('author', ""),
                'play_url': DouyinParser._get_first_url(music.get('play_url', {}))
            }

            images = data.get('images', [])
            video_info = data.get('video', {})
            duration_ms = data.get('duration', 0)
            duration_s = duration_ms / 1000.0

            if images:
                media_info = {
                    'cover_url': "",
                    'play_url': "",
                    'width': 0,
                    'height': 0,
                    'duration': duration_s,
                    'images': []
                }
                for img in images:
                    img_info = {
                        'url': DouyinParser._get_best_image_url(img),
                        'width': img.get('width', 0),
                        'height': img.get('height', 0),
                        'video': None
                    }
                    vid_data = img.get('video')
                    if vid_data and isinstance(vid_data, dict):
                        # 对于二次处理，获取主URL和备用URL
                        main_url, backup_urls = DouyinParser._get_best_play_url(vid_data, return_backups=True)
                        img_info['video'] = {
                            'url': main_url,
                            'width': vid_data.get('width', 0),
                            'height': vid_data.get('height', 0),
                            'backup_urls': backup_urls  # 添加备用URL
                        }
                    media_info['images'].append(img_info)
            else:
                # 对于普通视频，获取主URL和备用URL
                main_url, backup_urls = DouyinParser._get_best_play_url(video_info, return_backups=True)
                media_info = {
                    'cover_url': DouyinParser._get_first_url(video_info.get('cover', {})),
                    'play_url': main_url,
                    'width': video_info.get('width', 0),
                    'height': video_info.get('height', 0),
                    'duration': duration_s,
                    'images': [],
                    'backup_urls': backup_urls  # 添加备用URL
                }

            return {
                'base_info': {
                    'aweme_id': aweme_id,
                    'desc': desc,
                    'create_time': create_time_str
                },
                'author_info': author_info,
                'location_info': location_info,
                'statistics': stats,
                'music_info': music_info,
                'media_info': media_info
            }
        except Exception as e:
            logger.error(f"parse_aweme error: {str(e)}")
            return {}

    @staticmethod
    def parse_user(resp_data):
        try:
            data = resp_data.get('data', {}) if resp_data else {}
            if not isinstance(data, dict):
                return None
            
            author = data.get('author', {})
            if not author or not author.get('uid') or not author.get('sec_uid'):
                return None
            
            user_data = {
                'uid': author.get('uid'),
                'sec_uid': author.get('sec_uid'),
                'short_id': author.get('short_id'),
                'unique_id': author.get('unique_id', ""),
                'nickname': author.get('nickname'),
                'signature': author.get('signature'),
                'user_age': author.get('user_age'),
                'avatar_thumb_uri': author.get('avatar_thumb', {}).get('uri'),
                'avatar_thumb_url': (author.get('avatar_thumb', {}).get("url_list") or [None])[0],
                'create_time': datetime.fromtimestamp(author.get('create_time', 0)).isoformat() if author.get('create_time') else None,
                'follower_count': author.get('follower_count', 0),
                'following_count': author.get('following_count', 0),
                'total_favorited': author.get('total_favorited', 0),
                'favoriting_count': author.get('favoriting_count', 0),
                'status': author.get('status', 1),
                'verification_type': author.get('verification_type'),
                'user_canceled': author.get('user_canceled', False),
                'mate_add_permission': author.get('mate_add_permission'),
                'custom_verify': author.get('custom_verify'),
                'enterprise_verify_reason': author.get('enterprise_verify_reason'),
                'prevent_download': author.get('prevent_download', False),
                'contacts_status': author.get('contacts_status'),
                'cover_url': (author.get('cover_url', [{}])[0].get('url_list') or [None])[0],
                'last_fetched_at': datetime.now().isoformat()
            }
            
            return user_data
        except Exception as e:
            logger.error(f"parse_user error: {str(e)}")
            return None

# =============== 解析和下载函数 ===============
def parse_douyin_work(resp_data):
    return DouyinParser.parse_aweme(resp_data)

def parse_douyin_user(resp_data):
    return DouyinParser.parse_user(resp_data)

def get_random_cookie():
    """从cookie池中随机获取一个cookie"""
    cookie_pool_path = '/home/<USER>/API/local-singbox/config/cookie_pool_multi.json'
    try:
        with open(cookie_pool_path, 'r') as f:
            cookie_data = json.load(f)
            cookies = cookie_data.get('cookies', [])
            if cookies:
                return random.choice(cookies)
    except Exception as e:
        logger.warning(f"读取cookie池失败: {e}")
    return None

async def download_file(url, max_retries=3, backup_urls=None):
    """
    下载文件，支持备用URL
    Args:
        url: 主URL
        max_retries: 每个URL的最大重试次数
        backup_urls: 备用URL列表，当主URL遇到403错误时使用
    """
    attempt = 1
    current_url = url
    tried_urls = set()  # 记录已尝试过的URL
    
    # 备用URL为空时初始化为空列表
    if backup_urls is None:
        backup_urls = []
    
    # 备用URL队列
    backup_queue = list(backup_urls)
    
    while True:
        if current_url in tried_urls:
            # 如果当前URL已经尝试过，跳过
            if backup_queue:
                current_url = backup_queue.pop(0)
                logger.info(f"[downloadFile] 切换到备用URL: {current_url}")
                attempt = 1  # 重置尝试次数
                continue
            else:
                logger.error("[downloadFile] 所有URL都已尝试失败")
                return None
                
        tried_urls.add(current_url)
        
        try:
            logger.info(f"[downloadFile] GET {current_url}, attempt {attempt}")
            
            # 从cookie池获取随机cookie
            cookie = get_random_cookie()
            
            # 构建请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://www.douyin.com/',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Range': 'bytes=0-',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'video',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'cross-site'
            }
            
            # 如果有cookie，添加到请求头
            if cookie:
                headers['Cookie'] = cookie
                logger.debug(f"[downloadFile] 使用cookie池中的cookie")
            else:
                logger.warning(f"[downloadFile] 未能获取cookie，将不使用cookie进行请求")
            
            async with httpx.AsyncClient(timeout=10.0, follow_redirects=True) as client:
                response = await client.get(current_url, headers=headers)
                
                # 处理HTTP状态码错误
                if not response.is_success:
                    # 403错误特殊处理，但仍需考虑尝试次数
                    if response.status_code == 403:
                        logger.warning(f"[downloadFile] 遇到403错误 (attempt {attempt}/{max_retries})")
                        if attempt < max_retries:
                            # 403错误但尝试次数未达到上限，继续重试当前URL
                            tried_urls.remove(current_url)
                            wait_sec = 2 ** attempt
                            logger.info(f"等待 {wait_sec} 秒后重试同一URL...")
                            await asyncio.sleep(wait_sec)
                            attempt += 1
                            continue
                        elif backup_queue:
                            # 达到尝试上限且有备用URL，切换URL
                            logger.warning(f"[downloadFile] 达到当前URL的最大重试次数，切换到备用URL")
                            current_url = backup_queue.pop(0)
                            attempt = 1  # 重置尝试次数
                            continue
                        else:
                            # 达到尝试上限且无备用URL，失败
                            logger.error(f"[downloadFile] 403错误且达到最大重试次数 ({max_retries})，无备用URL")
                            return None
                    
                    # 其他HTTP错误
                    raise Exception(f"HTTP error! status: {response.status_code}")
                
                binary_data = response.content
                content_type = response.headers.get("content-type", "")
                return {
                    "binary_data": binary_data,
                    "content_type": content_type,
                    "url": current_url  # 返回实际下载的 URL（可能是重定向后的）
                }
        except Exception as error:
            error_str = str(error)
            
            # 连接中断错误处理
            if "peer closed connection without sending complete message" in error_str:
                logger.warning(f"[downloadFile] 连接中断错误: {error_str}")
                logger.info(f"[downloadFile] 将在2秒后进行第 {attempt+1} 次尝试...")
                # 从已尝试URL集合中移除当前URL，允许再次尝试同一URL
                tried_urls.remove(current_url)
                await asyncio.sleep(2)
                attempt += 1
                continue
            
            # 检查是否为403错误
            if "403" in error_str:
                logger.warning(f"[downloadFile] 从异常中检测到403错误 (attempt {attempt}/{max_retries})")
                if attempt < max_retries:
                    # 403错误但尝试次数未达到上限，继续重试当前URL
                    tried_urls.remove(current_url)
                    wait_sec = 2 ** attempt
                    logger.info(f"等待 {wait_sec} 秒后重试同一URL...")
                    await asyncio.sleep(wait_sec)
                    attempt += 1
                    continue
                elif backup_queue:
                    # 达到尝试上限且有备用URL，切换URL
                    logger.warning(f"[downloadFile] 达到当前URL的最大重试次数，切换到备用URL")
                    current_url = backup_queue.pop(0)
                    attempt = 1  # 重置尝试次数
                    continue
                else:
                    # 达到尝试上限且无备用URL，失败
                    logger.error(f"[downloadFile] 403错误且达到最大重试次数 ({max_retries})，无备用URL")
                    return None
                
            logger.warning(f"[downloadFile] 错误: {error_str}")
            if attempt < max_retries:
                wait_sec = 2 ** attempt
                logger.info(f"等待 {wait_sec} 秒后重试...")
                # 对于其他错误，也允许重试同一URL
                tried_urls.remove(current_url)
                await asyncio.sleep(wait_sec)
                attempt += 1
            else:
                # 当前URL达到最大重试次数，尝试备用URL
                if backup_queue:
                    current_url = backup_queue.pop(0)
                    logger.info(f"[downloadFile] 切换到备用URL: {current_url}")
                    attempt = 1  # 重置尝试次数
                else:
                    logger.error(f"[downloadFile] 达到最大重试次数 ({max_retries})，下载失败")
                    return None
    return None

def build_caption_for_single(parsed_data):
    base = parsed_data.get('base_info', {})
    author = parsed_data.get('author_info', {})
    stats = parsed_data.get('statistics', {})
    music = parsed_data.get('music_info', {})
    location = parsed_data.get('location_info', {})

    lines = []
    if base.get('aweme_id'):
        lines.append(f"作品ID: {base.get('aweme_id')}")
    if base.get('desc'):
        lines.append(f"描述: {base.get('desc')}")
    if base.get('create_time'):
        lines.append(f"发布时间: {base.get('create_time')} (北京时间)")
    if author.get('nickname'):
        lines.append(f"作者昵称: {author.get('nickname')}")
    if author.get('unique_id'):
        lines.append(f"抖音号: {author.get('unique_id')}")
    if author.get('uid'):
        lines.append(f"作者UID: {author.get('uid')}")

    fc = author.get('follower_count')
    tf = author.get('total_favorited')
    if fc is not None and tf is not None:
        lines.append(f"粉丝数: {fc} | 获赞: {tf}")

    province = location.get('province', "")
    city = location.get('city', "")
    if province or city:
        lines.append(f"地点: {province} {city}".strip())

    digg = stats.get('digg_count')
    cmt = stats.get('comment_count')
    shr = stats.get('share_count')
    col = stats.get('collect_count')
    stats_parts = []
    if digg is not None:
        stats_parts.append(f"点赞: {digg}")
    if cmt is not None:
        stats_parts.append(f"评论: {cmt}")
    if shr is not None:
        stats_parts.append(f"分享: {shr}")
    if col is not None:
        stats_parts.append(f"收藏: {col}")
    if stats_parts:
        lines.append(" | ".join(stats_parts))

    if music.get('title') and music.get('author'):
        lines.append(f"音乐: {music.get('title')} - {music.get('author')}")

    return "\n".join(lines).strip()

# =============== 文件处理函数 ===============
def is_image_file(filename):
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.heic', '.heif', '.bmp', '.tiff', '.tif', '.svg']

def is_video_file(filename):
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.mpeg', '.mpg', '.ogv', '.flv', '.m4v', '.3gp']

def is_audio_file(filename):
    ext = os.path.splitext(filename.lower())[1]
    return ext in ['.mp3', '.wav', '.m4a', '.flac', '.aac', '.ogg', '.weba', '.opus', '.wma']

def is_heic_file(filename):
    ext = os.path.splitext(filename.lower())[1]
    return ext == '.heic'

def get_file_extension_from_url(url):
    """从 URL 中提取文件扩展名"""
    try:
        # 解析 URL，获取路径部分
        from urllib.parse import urlparse
        parsed = urlparse(url)
        path = parsed.path
        
        # 提取扩展名
        if path:
            # 移除查询参数
            path = path.split('?')[0]
            # 获取扩展名
            ext = os.path.splitext(path)[1].lower()
            if ext and ext.startswith('.'):
                return ext
    except Exception as e:
        logger.debug(f"从 URL 提取扩展名失败: {e}")
    return None

def get_file_extension_from_content_type(content_type):
    """根据 content-type 推断文件扩展名"""
    # content-type 到扩展名的映射
    content_type_map = {
        # 图片格式
        'image/jpeg': '.jpg',
        'image/jpg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'image/heic': '.heic',
        'image/heif': '.heif',
        'image/bmp': '.bmp',
        'image/tiff': '.tiff',
        'image/svg+xml': '.svg',
        
        # 视频格式
        'video/mp4': '.mp4',
        'video/mpeg': '.mpeg',
        'video/quicktime': '.mov',
        'video/x-msvideo': '.avi',
        'video/x-matroska': '.mkv',
        'video/webm': '.webm',
        'video/ogg': '.ogv',
        'video/x-flv': '.flv',
        
        # 音频格式
        'audio/mpeg': '.mp3',
        'audio/mp3': '.mp3',
        'audio/wav': '.wav',
        'audio/x-wav': '.wav',
        'audio/mp4': '.m4a',
        'audio/x-m4a': '.m4a',
        'audio/flac': '.flac',
        'audio/aac': '.aac',
        'audio/ogg': '.ogg',
        'audio/webm': '.weba',
    }
    
    if content_type:
        # 移除 charset 等参数
        content_type = content_type.split(';')[0].strip().lower()
        return content_type_map.get(content_type)
    return None

def determine_file_extension(url, content_type, default_ext):
    """
    根据 URL 和 content-type 确定文件扩展名
    优先级：URL中的扩展名 > content-type推断 > 默认扩展名
    """
    # 首先尝试从 URL 获取扩展名
    ext = get_file_extension_from_url(url)
    if ext:
        logger.debug(f"从 URL 获取到扩展名: {ext}")
        return ext
    
    # 其次尝试从 content-type 获取
    ext = get_file_extension_from_content_type(content_type)
    if ext:
        logger.debug(f"从 content-type '{content_type}' 推断扩展名: {ext}")
        return ext
    
    # 最后使用默认扩展名
    logger.debug(f"无法确定扩展名，使用默认值: {default_ext}")
    return default_ext

async def convert_heic_to_png(heic_path):
    """将 HEIC 文件转换为 PNG 格式"""
    try:
        # 注册 HEIC 支持
        pillow_heif.register_heif_opener()
        
        # 生成 PNG 文件路径
        png_path = os.path.splitext(heic_path)[0] + '_converted.png'
        
        # 打开 HEIC 文件并转换为 PNG
        with Image.open(heic_path) as img:
            # 如果图片有 EXIF 方向信息，自动旋转
            img = img.convert('RGB')
            img.save(png_path, 'PNG', optimize=True)
        
        logger.info(f"成功将 HEIC 文件转换为 PNG: {heic_path} -> {png_path}")
        return png_path
    except Exception as e:
        logger.error(f"转换 HEIC 文件失败 {heic_path}: {str(e)}")
        return None

async def convert_image_to_png(image_path):
    """将任意图片格式转换为 PNG 格式"""
    try:
        # 如果是 HEIC，需要先注册支持
        if is_heic_file(image_path):
            pillow_heif.register_heif_opener()
        
        # 生成 PNG 文件路径
        png_path = os.path.splitext(image_path)[0] + '_converted.png'
        
        # 打开图片并转换为 PNG
        with Image.open(image_path) as img:
            # 转换为 RGB（处理 RGBA、灰度等格式）
            if img.mode not in ('RGB', 'RGBA'):
                img = img.convert('RGB')
            img.save(png_path, 'PNG', optimize=True)
        
        logger.info(f"成功将图片转换为 PNG: {image_path} -> {png_path}")
        return png_path
    except Exception as e:
        logger.error(f"转换图片到 PNG 失败 {image_path}: {str(e)}")
        return None

async def convert_video_to_mp4(video_path):
    """使用 ffmpeg 将视频转换为 MP4 格式"""
    try:
        mp4_path = os.path.splitext(video_path)[0] + '_converted.mp4'
        
        # 使用 ffmpeg 转换视频
        cmd = f'ffmpeg -i "{video_path}" -c:v libx264 -c:a aac -movflags +faststart -y "{mp4_path}"'
        logger.info(f"执行视频转换命令: {cmd}")
        
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=300.0)  # 5分钟超时
            if process.returncode == 0:
                logger.info(f"成功将视频转换为 MP4: {video_path} -> {mp4_path}")
                return mp4_path
            else:
                logger.error(f"视频转换失败: {stderr.decode()}")
                return None
        except asyncio.TimeoutError:
            logger.error("视频转换超时（5分钟）")
            try:
                process.terminate()
                await asyncio.wait_for(process.wait(), timeout=1.0)
            except (asyncio.TimeoutError, ProcessLookupError):
                try:
                    process.kill()
                except ProcessLookupError:
                    pass
            return None
    except Exception as e:
        logger.error(f"转换视频到 MP4 失败 {video_path}: {str(e)}")
        return None

async def convert_audio_to_mp3(audio_path):
    """使用 ffmpeg 将音频转换为 MP3 格式（sox 可能不是所有系统都有）"""
    try:
        mp3_path = os.path.splitext(audio_path)[0] + '_converted.mp3'
        
        # 使用 ffmpeg 转换音频（更通用）
        cmd = f'ffmpeg -i "{audio_path}" -acodec mp3 -ab 192k -y "{mp3_path}"'
        logger.info(f"执行音频转换命令: {cmd}")
        
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=60.0)  # 1分钟超时
            if process.returncode == 0:
                logger.info(f"成功将音频转换为 MP3: {audio_path} -> {mp3_path}")
                return mp3_path
            else:
                logger.error(f"音频转换失败: {stderr.decode()}")
                return None
        except asyncio.TimeoutError:
            logger.error("音频转换超时（1分钟）")
            try:
                process.terminate()
                await asyncio.wait_for(process.wait(), timeout=1.0)
            except (asyncio.TimeoutError, ProcessLookupError):
                try:
                    process.kill()
                except ProcessLookupError:
                    pass
            return None
    except Exception as e:
        logger.error(f"转换音频到 MP3 失败 {audio_path}: {str(e)}")
        return None

async def get_video_dimensions(video_path):
    try:
        cmd = f'ffprobe -v error -select_streams v:0 -show_entries stream=width,height -of json "{video_path}"'
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        if process.returncode != 0:
            logger.error(f"FFprobe error: {stderr.decode()}")
            return None, None
        data = json.loads(stdout.decode())
        stream = data.get('streams', [{}])[0]
        return stream.get('width'), stream.get('height')
    except Exception as e:
        logger.error(f"Error getting video dimensions: {str(e)}")
        return None, None

async def get_video_file_size_in_mb(file_path):
    try:
        stat_result = os.stat(file_path)
        file_size_mb = stat_result.st_size / (1024 * 1024)
        return file_size_mb
    except Exception as e:
        logger.error(f"获取文件大小失败: {str(e)}")
        return 0

async def kill_ffmpeg_processes():
    try:
        if os.name == 'nt':
            cmd = 'taskkill /F /IM ffmpeg.exe'
        else:
            cmd = "pkill -9 ffmpeg"
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        if process.returncode == 0:
            logger.info("成功终止所有卡住的ffmpeg进程。")
        else:
            logger.warning(f"终止ffmpeg进程返回非零代码: {process.returncode}")
            if stderr:
                logger.debug(f"终止进程stderr: {stderr.decode().strip()}")
    except Exception as e:
        logger.error(f"终止ffmpeg进程时出错: {str(e)}")

async def extract_video_thumbnail(video_path, output_path=None):
    if output_path is None:
        base_path = os.path.splitext(video_path)[0]
        output_path = f"{base_path}_thumb.jpg"
    process = None
    try:
        cmd = f'ffmpeg -threads 1 -ss 1 -i "{video_path}" -vframes 1 -q:v 5 -f image2 "{output_path}" -y'
        logger.info(f"正在执行ffmpeg命令: {cmd}")
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=10.0)
            first_attempt_success = process.returncode == 0
        except asyncio.TimeoutError:
            logger.warning(f"FFmpeg 生成预览图超时 (10秒)，正在终止进程...")
            try:
                process.terminate()
                await asyncio.wait_for(process.wait(), timeout=1.0)
            except (asyncio.TimeoutError, ProcessLookupError):
                try:
                    process.kill()
                except ProcessLookupError:
                    pass
            await kill_ffmpeg_processes()
            logger.warning(f"已终止超时的ffmpeg进程，跳过预览图生成")
            return None
        
        if not first_attempt_success:
            logger.warning(f"FFmpeg 提取第1秒失败，尝试提取第0.1秒...")
            try:
                if process and process.returncode is None:
                    process.terminate()
                    await asyncio.wait_for(process.wait(), timeout=0.5)
            except (asyncio.TimeoutError, ProcessLookupError):
                try:
                    process.kill()
                except ProcessLookupError:
                    pass
            cmd = f'ffmpeg -threads 1 -ss 0.1 -i "{video_path}" -vframes 1 -q:v 5 -f image2 "{output_path}" -y'
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            try:
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=10.0)
                second_attempt_success = process.returncode == 0
            except asyncio.TimeoutError:
                logger.warning("第二次尝试也超时，放弃生成预览图")
                try:
                    process.terminate()
                    await asyncio.wait_for(process.wait(), timeout=0.5)
                except (asyncio.TimeoutError, ProcessLookupError):
                    try:
                        process.kill()
                    except ProcessLookupError:
                        pass
                await kill_ffmpeg_processes()
                return None
            if not second_attempt_success:
                logger.error(f"FFmpeg 提取第0.1秒也失败: {stderr.decode() if stderr else '未知错误'}")
                return None

        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f"成功从视频提取缩略图: {output_path}")
            return output_path
        else:
            logger.error(f"提取缩略图失败: 输出文件为空或不存在")
            return None
    except Exception as e:
        logger.error(f"提取视频缩略图失败: {str(e)}")
        if process:
            try:
                process.terminate()
                await asyncio.wait_for(process.wait(), timeout=0.5)
            except (asyncio.TimeoutError, ProcessLookupError):
                try:
                    process.kill()
                except ProcessLookupError:
                    pass
        await kill_ffmpeg_processes()
        return None

async def download_music_file(music_url, output_dir, aweme_id):
    if not music_url:
        return None
    try:
        music_data = await download_file(music_url)
        if not music_data or not music_data.get("binary_data"):
            return None
        
        # 根据 URL 和 content-type 确定文件扩展名
        actual_url = music_data.get("url", music_url)
        content_type = music_data.get("content_type", "")
        ext = determine_file_extension(actual_url, content_type, '.mp3')
        
        music_path = os.path.join(output_dir, f"{aweme_id}_music{ext}")
        os.makedirs(output_dir, exist_ok=True)
        with open(music_path, "wb") as f:
            f.write(music_data.get("binary_data"))
        logger.info(f"成功下载音乐文件: {music_path} (扩展名: {ext})")
        return music_path
    except Exception as e:
        logger.error(f"下载音乐文件失败: {str(e)}")
        return None

async def create_zip_archive(files, output_path):
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in files:
                if os.path.exists(file):
                    zipf.write(file, os.path.basename(file))
                else:
                    logger.warning(f"归档时跳过不存在的文件: {file}")
        logger.info(f"ZIP 文件创建完成: {output_path}, 大小: {os.path.getsize(output_path)} bytes")
        return output_path
    except Exception as e:
        logger.error(f"创建 ZIP 归档错误: {str(e)}")
        return None

def get_bot_for_task():
    return primary_bot

def truncate_caption(caption, max_length=1000):
    """
    截断caption，确保不超过max_length字符。
    优先截断描述部分，保留其他重要信息，超过的部分用省略号替代。
    """
    if not caption or len(caption) <= max_length:
        return caption
    
    # 分割成行来分析
    lines = caption.split('\n')
    
    # 找到描述行（如果存在）
    desc_index = -1
    desc_line = ""
    for i, line in enumerate(lines):
        if line.startswith("描述:"):
            desc_index = i
            desc_line = line
            break
    
    # 如果没有找到描述行，或者描述行很短，就简单截断整个caption
    if desc_index == -1 or len(desc_line) < 100:
        return caption[:max_length-3] + "..."
    
    # 计算描述行之外的内容长度
    other_lines_length = sum(len(line) + 1 for i, line in enumerate(lines) if i != desc_index)
    
    # 计算可用于描述的字符数
    available_desc_length = max_length - other_lines_length - 3  # 3为"..."的长度
    
    # 如果可用长度为负，说明即使完全删除描述也超长，只能截断整个caption
    if available_desc_length <= 0:
        return caption[:max_length-3] + "..."
    
    # 截断描述行
    desc_prefix = "描述: "
    desc_content = desc_line[len(desc_prefix):]
    truncated_desc = desc_prefix + desc_content[:available_desc_length] + "..."
    
    # 替换原描述行
    lines[desc_index] = truncated_desc
    
    return '\n'.join(lines)

async def send_single_file(app, chat_id, file_path, caption, reply_markup=None):
    try:
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            logger.info(f"文件无效或大小为0: {file_path}")
            return None
        
        # 截断caption，避免超过Telegram限制
        if caption:
            caption = truncate_caption(caption)
            
        if is_video_file(file_path):
            file_size_mb = await get_video_file_size_in_mb(file_path)
            width, height = await get_video_dimensions(file_path)
            thumbnail_path = None
            if file_size_mb >= VIDEO_THUMBNAIL_THRESHOLD:
                logger.info(f"视频 {file_path} 大小为 {file_size_mb:.2f}MB，超过 {VIDEO_THUMBNAIL_THRESHOLD}MB 阈值，将生成预览图")
                try:
                    logger.info(f"为视频 {file_path} 生成预览图...")
                    thumbnail_path = await extract_video_thumbnail(file_path)
                    if not thumbnail_path or not os.path.exists(thumbnail_path):
                        logger.warning(f"未能成功生成视频 {file_path} 的预览图。")
                        thumbnail_path = None
                except Exception as thumb_err:
                    logger.error(f"生成视频 {file_path} 预览图时出错: {thumb_err}")
                    thumbnail_path = None
            else:
                logger.info(f"视频 {file_path} 大小为 {file_size_mb:.2f}MB，未超过阈值，跳过生成预览图")

            converted_video = None
            try:
                message = await app.send_video(
                    chat_id,
                    file_path,
                    caption=caption,
                    width=width,
                    height=height,
                    thumb=thumbnail_path,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
            except Exception as e:
                # 如果发送失败，尝试转换为 MP4 后重试
                error_str = str(e)
                # 捕获所有可能的视频相关错误
                if any(err in error_str for err in ["VIDEO_", "MEDIA_INVALID", "FILE_INVALID", "MEDIA_EMPTY"]):
                    logger.warning(f"视频发送失败 ({error_str})，尝试转换为 MP4: {file_path}")
                    converted_video = await convert_video_to_mp4(file_path)
                    if converted_video and os.path.exists(converted_video):
                        # 重新获取转换后视频的尺寸
                        width, height = await get_video_dimensions(converted_video)
                        try:
                            message = await app.send_video(
                                chat_id,
                                converted_video,
                                caption=caption,
                                width=width,
                                height=height,
                                thumb=thumbnail_path,
                                reply_markup=reply_markup,
                                parse_mode=ParseMode.HTML
                            )
                        except Exception as retry_err:
                            logger.error(f"转换为 MP4 后仍然发送失败: {retry_err}")
                            raise
                    else:
                        raise
                else:
                    raise
            if DELETE_FILES:
                if thumbnail_path and os.path.exists(thumbnail_path):
                    try:
                        os.unlink(thumbnail_path)
                    except Exception as del_err:
                        logger.error(f"删除临时缩略图 {thumbnail_path} 失败: {del_err}")
                if converted_video and os.path.exists(converted_video):
                    try:
                        os.unlink(converted_video)
                        logger.debug(f"已删除转换的视频文件: {converted_video}")
                    except Exception as del_err:
                        logger.error(f"删除转换的视频文件 {converted_video} 失败: {del_err}")
            
            # 检查 video 或 animation file_id
            if message:
                if hasattr(message, 'video') and message.video:
                    return message.video.file_id
                elif hasattr(message, 'animation') and message.animation:
                    logger.info(f"视频 {file_path} 被 Telegram 作为动画发送，获取 animation.file_id")
                    return message.animation.file_id
                else:
                    logger.warning(f"发送视频 {file_path} 后返回的消息中未找到 video 或 animation 属性。 Message: {message}")
                    return None
            else:
                logger.warning(f"发送视频 {file_path} 未返回有效消息对象。")
                return None

        elif is_image_file(file_path):
            # 如果是 HEIC 文件，先转换为 PNG
            actual_file_path = file_path
            converted_file = None
            if is_heic_file(file_path):
                converted_file = await convert_heic_to_png(file_path)
                if converted_file and os.path.exists(converted_file):
                    actual_file_path = converted_file
                else:
                    logger.warning(f"HEIC 文件转换失败，尝试发送原始文件: {file_path}")
            
            try:
                message = await app.send_photo(
                    chat_id,
                    actual_file_path,
                    caption=caption,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                if message and hasattr(message, 'photo'):
                    return message.photo.file_id
                return None
            except Exception as e:
                # 如果发送失败，尝试转换为 PNG 后重试
                error_str = str(e)
                # 捕获所有可能的图片相关错误
                if any(err in error_str for err in ["PHOTO_", "IMAGE_", "MEDIA_INVALID", "FILE_INVALID", "MEDIA_EMPTY"]):
                    logger.warning(f"图片发送失败 ({error_str})，尝试转换为 PNG: {file_path}")
                    if not converted_file:  # 如果还没转换过
                        converted_file = await convert_image_to_png(file_path)
                        if converted_file and os.path.exists(converted_file):
                            try:
                                message = await app.send_photo(
                                    chat_id,
                                    converted_file,
                                    caption=caption,
                                    reply_markup=reply_markup,
                                    parse_mode=ParseMode.HTML
                                )
                                if message and hasattr(message, 'photo'):
                                    return message.photo.file_id
                            except Exception as retry_err:
                                logger.error(f"转换为 PNG 后仍然发送失败: {retry_err}")
                raise
            finally:
                # 清理转换的临时文件
                if DELETE_FILES and converted_file and os.path.exists(converted_file):
                    try:
                        os.unlink(converted_file)
                        logger.debug(f"已删除转换的临时文件: {converted_file}")
                    except Exception as del_err:
                        logger.error(f"删除转换的临时文件 {converted_file} 失败: {del_err}")

        elif is_audio_file(file_path):
            converted_audio = None
            try:
                message = await app.send_audio(
                    chat_id,
                    file_path,
                    caption=caption,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                if message and hasattr(message, 'audio'):
                    return message.audio.file_id
                return None
            except Exception as e:
                # 如果发送失败，尝试转换为 MP3 后重试
                error_str = str(e)
                # 捕获所有可能的音频相关错误
                if any(err in error_str for err in ["AUDIO_", "MEDIA_INVALID", "FILE_INVALID", "MEDIA_EMPTY"]):
                    logger.warning(f"音频发送失败 ({error_str})，尝试转换为 MP3: {file_path}")
                    converted_audio = await convert_audio_to_mp3(file_path)
                    if converted_audio and os.path.exists(converted_audio):
                        try:
                            message = await app.send_audio(
                                chat_id,
                                converted_audio,
                                caption=caption,
                                reply_markup=reply_markup,
                                parse_mode=ParseMode.HTML
                            )
                            if message and hasattr(message, 'audio'):
                                return message.audio.file_id
                            return None
                        except Exception as retry_err:
                            logger.error(f"转换为 MP3 后仍然发送失败: {retry_err}")
                            raise
                    else:
                        raise
                else:
                    raise
            finally:
                # 清理转换的临时文件
                if DELETE_FILES and converted_audio and os.path.exists(converted_audio):
                    try:
                        os.unlink(converted_audio)
                        logger.debug(f"已删除转换的音频文件: {converted_audio}")
                    except Exception as del_err:
                        logger.error(f"删除转换的音频文件 {converted_audio} 失败: {del_err}")

        else:
            message = await app.send_document(
                chat_id,
                file_path,
                caption=caption,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )
            if message and hasattr(message, 'document'):
                return message.document.file_id
            return None
    except Exception as e:
        logger.error(f"sendSingleFile error: {str(e)}")
        return None

async def send_media_group(app, chat_id, media_files, caption=None):
    media_group = []
    generated_thumbnails = []
    try:
        # 截断caption，避免超过Telegram限制
        if caption:
            caption = truncate_caption(caption)
            
        converted_files = []  # 跟踪转换的文件以便后续清理
        for file_path in media_files:
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                continue
            if is_image_file(file_path):
                # 如果是 HEIC 文件，先转换为 PNG
                if is_heic_file(file_path):
                    png_path = await convert_heic_to_png(file_path)
                    if png_path and os.path.exists(png_path):
                        media_group.append(InputMediaPhoto(media=png_path))
                        converted_files.append(png_path)
                    else:
                        logger.warning(f"跳过无法转换的 HEIC 文件: {file_path}")
                else:
                    media_group.append(InputMediaPhoto(media=file_path))
            elif is_video_file(file_path):
                width, height = await get_video_dimensions(file_path)
                file_size_mb = await get_video_file_size_in_mb(file_path)
                thumbnail_path = None
                if file_size_mb >= VIDEO_THUMBNAIL_THRESHOLD:
                    logger.info(f"媒体组中视频 {file_path} 大小为 {file_size_mb:.2f}MB，超过阈值，将生成预览图")
                    try:
                        logger.info(f"为媒体组中的视频 {file_path} 生成预览图...")
                        thumbnail_path = await extract_video_thumbnail(file_path)
                        if not thumbnail_path or not os.path.exists(thumbnail_path):
                            logger.warning(f"未能成功生成视频 {file_path} 的预览图。")
                            thumbnail_path = None
                        else:
                            generated_thumbnails.append(thumbnail_path)
                    except Exception as thumb_err:
                        logger.error(f"生成视频 {file_path} 预览图时出错: {thumb_err}")
                        thumbnail_path = None
                media_group.append(InputMediaVideo(
                    media=file_path,
                    width=width,
                    height=height,
                    thumb=thumbnail_path
                ))
            elif is_audio_file(file_path):
                media_group.append(InputMediaAudio(media=file_path))
            else:
                media_group.append(InputMediaDocument(media=file_path))

        if caption and media_group:
            media_group[-1].caption = caption
            media_group[-1].parse_mode = ParseMode.HTML

        if not media_group:
            logger.warning("没有有效的媒体文件可发送")
            return []

        try:
            messages = await app.send_media_group(chat_id, media_group)
        except Exception as e:
            # 如果媒体组发送失败，尝试转换格式后重试
            error_msg = str(e)
            # 捕获所有可能的媒体相关错误
            if any(err in error_msg for err in ["PHOTO_", "IMAGE_", "VIDEO_", "AUDIO_", "MEDIA_INVALID", "FILE_INVALID", "MEDIA_EMPTY"]):
                logger.warning(f"媒体组发送失败 ({error_msg})，尝试转换格式后重试")
                
                # 重新构建媒体组，转换不支持的格式
                new_media_group = []
                new_converted_files = []
                
                # 需要原始文件路径来转换，从前面收集
                original_files = []
                for i, file_path in enumerate(media_files):
                    if os.path.exists(file_path):
                        original_files.append(file_path)
                
                for i, media_item in enumerate(media_group):
                    if i < len(original_files):
                        file_path = original_files[i]
                        if is_image_file(file_path) and not is_heic_file(file_path):
                            # 转换图片为 PNG（HEIC 应该已经被转换了）
                            converted_img = await convert_image_to_png(file_path)
                            if converted_img and os.path.exists(converted_img):
                                new_media_group.append(InputMediaPhoto(media=converted_img))
                                new_converted_files.append(converted_img)
                                logger.info(f"转换图片 {file_path} 为 PNG")
                            else:
                                new_media_group.append(media_item)
                        elif is_video_file(file_path):
                            # 转换视频为 MP4
                            converted_vid = await convert_video_to_mp4(file_path)
                            if converted_vid and os.path.exists(converted_vid):
                                width, height = await get_video_dimensions(converted_vid)
                                # 保留原缩略图（如果有）
                                thumb = getattr(media_item, 'thumb', None)
                                new_media_group.append(InputMediaVideo(
                                    media=converted_vid,
                                    width=width,
                                    height=height,
                                    thumb=thumb
                                ))
                                new_converted_files.append(converted_vid)
                                logger.info(f"转换视频 {file_path} 为 MP4")
                            else:
                                new_media_group.append(media_item)
                        else:
                            new_media_group.append(media_item)
                    else:
                        new_media_group.append(media_item)
                
                # 重新添加 caption
                if caption and new_media_group:
                    new_media_group[-1].caption = caption
                    new_media_group[-1].parse_mode = ParseMode.HTML
                
                # 合并转换文件列表
                converted_files.extend(new_converted_files)
                
                # 重试发送
                messages = await app.send_media_group(chat_id, new_media_group)
            else:
                raise
        
        file_ids = []
        for msg in messages:
            if hasattr(msg, 'photo') and msg.photo:
                # photo是一个数组，包含不同尺寸的照片，取最后一个（最大尺寸）
                file_ids.append(msg.photo[-1].file_id if isinstance(msg.photo, list) else msg.photo.file_id)
            elif hasattr(msg, 'video') and msg.video:
                file_ids.append(msg.video.file_id)
            elif hasattr(msg, 'audio') and msg.audio:
                file_ids.append(msg.audio.file_id)
            elif hasattr(msg, 'document') and msg.document:
                file_ids.append(msg.document.file_id)
        return file_ids
    except Exception as e:
        logger.error(f"发送媒体组失败: {str(e)}")
        return []
    finally:
        if DELETE_FILES:
            for thumb in generated_thumbnails:
                if thumb and os.path.exists(thumb):
                    try:
                        os.unlink(thumb)
                    except Exception as del_err:
                        logger.error(f"删除媒体组临时缩略图 {thumb} 失败: {del_err}")
            # 删除转换的 PNG 文件
            for converted_file in converted_files:
                if converted_file and os.path.exists(converted_file):
                    try:
                        os.unlink(converted_file)
                        logger.debug(f"已删除转换的临时文件: {converted_file}")
                    except Exception as del_err:
                        logger.error(f"删除转换的临时文件 {converted_file} 失败: {del_err}")

def chunk_list(array, size):
    return [array[i:i + size] for i in range(0, len(array), size)]

async def send_media_files(
    app,
    chat_id,
    media_files,
    douyin_url,
    caption_text,
    sec_uid,
    music_link="",
    from_author_posts=False,
    user_settings=None,
    aweme_id=None,
    liked_by_hulu=False,
    liked_by_500=False,
    saved_by_hulu=False,
    saved_by_500=False,
    is_window_task=False,
    is_large_task_mode=False,
    is_liked_video=False,
    failed_reason=None  # 新增参数，用于同时更新failed字段
):
    actual_bot_token = BOT_TOKEN
    if not user_settings:
        user_settings = {"send_file": True}
    music_processed = False
    music_file_id = None
    db_updated = False

    valid_files = [f for f in media_files if os.path.exists(f) and os.path.getsize(f) > 0]
    if not valid_files:
        logger.info("无可发送文件。")
        return ""

    all_file_ids = []
    output_dir = os.path.dirname(valid_files[0]) if valid_files else 'downloads'
    
    # 第一步：立即创建 ZIP 文件（包含原始下载文件）
    zip_file = None
    zip_path = None
    if user_settings.get("send_file", True) and valid_files:
        try:
            # 使用所有原始下载的文件创建 ZIP
            files_to_zip = valid_files.copy()
            if files_to_zip:
                base_name = aweme_id or os.path.basename(files_to_zip[0]).split('_')[0] or "douyin"
                zip_path = os.path.join(output_dir, f"{base_name}.zip")
                logger.info(f"正在创建包含 {len(files_to_zip)} 个原始文件的 ZIP: {zip_path}")
                zip_file = await create_zip_archive(files_to_zip, zip_path)
                if not (zip_file and os.path.exists(zip_file) and os.path.getsize(zip_file) > 0):
                    logger.warning(f"创建的 ZIP 文件无效或为空: {zip_path}")
                    zip_file = None
                else:
                    logger.info(f"成功创建 ZIP 文件: {zip_path}, 大小: {os.path.getsize(zip_file)} bytes")
        except Exception as e:
            logger.error(f"创建 ZIP 文件失败: {str(e)}")
            zip_file = None
    
    # 第二步：发送 ZIP 文件（如果创建成功）
    if zip_file and os.path.exists(zip_file) and os.path.getsize(zip_file) > 0:
        try:
            reply_markup = None
            if aweme_id:
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("打开作品", url=f"https://www.douyin.com/video/{aweme_id}")]
                ])
            # 发送ZIP文件并处理可能的FloodWait
            while True:
                try:
                    # 根据任务类型设置 caption
                    zip_caption = None
                    if aweme_id:
                        if is_window_task:
                            zip_caption = f"本作品（{aweme_id}）是窗口作品"
                        elif is_large_task_mode:
                            zip_caption = f"本作品（{aweme_id}）是large作品"
                        elif is_liked_video or liked_by_hulu or liked_by_500:
                            zip_caption = f"本作品（{aweme_id}）是点赞收藏作品"
                        elif saved_by_hulu or saved_by_500:
                            zip_caption = f"本作品（{aweme_id}）是点赞收藏作品"
                        else:
                            zip_caption = f"本作品（{aweme_id}）是常规监听作品"
                    
                    message = await app.send_document(
                        chat_id,
                        zip_file,
                        caption=zip_caption,
                        reply_markup=reply_markup
                    )
                    if message and hasattr(message, 'document'):
                        all_file_ids.append(message.document.file_id)
                    logger.info(f"成功发送 ZIP 文件: {zip_path}")
                    break  # 成功发送，退出循环
                except FloodWait as e:
                    wait_time = e.value
                    logger.warning(f"Telegram API FloodWait: 要求等待 {wait_time} 秒. 将等待 {wait_time + 1} 秒后重试发送 ZIP 文件...")
                    await asyncio.sleep(wait_time + 1)
                    # 继续循环重试
            
            # 立即删除 ZIP 文件
            if DELETE_FILES and os.path.exists(zip_path):
                os.unlink(zip_path)
                logger.info(f"已删除 ZIP 文件: {zip_path}")
        except Exception as e:
            logger.error(f"发送ZIP失败: {str(e)}")
            # 确保出错时也删除 ZIP 文件
            if DELETE_FILES and zip_path and os.path.exists(zip_path):
                try:
                    os.unlink(zip_path)
                    logger.info(f"已删除失败的 ZIP 文件: {zip_path}")
                except Exception as del_err:
                    logger.error(f"删除 ZIP 文件失败: {del_err}")
    
    # 第三步：发送媒体文件到 Telegram（可能会进行格式转换）
    music_files = [f for f in valid_files if f.endswith("_music.mp3")]
    for music_file in music_files:
        music_file_id = await send_single_file(app, chat_id, music_file, "[音乐文件]")
        if music_file_id:
            all_file_ids.append(music_file_id)
            music_processed = True
            valid_files.remove(music_file)
    
    non_music_files = [f for f in valid_files if f not in music_files]
    if len(non_music_files) == 1:
        file_id = await send_single_file(app, chat_id, non_music_files[0], caption_text)
        if file_id:
            all_file_ids.append(file_id)
    elif len(non_music_files) > 1:
        media_files_to_send = non_music_files[:10]
        logger.info(f"作品 {aweme_id or 'N/A'}: 共有 {len(non_music_files)} 个媒体文件，只发送前 {len(media_files_to_send)} 个")
        file_ids = await send_media_group(app, chat_id, media_files_to_send, caption_text)
        all_file_ids.extend(file_ids)

    if aweme_id and all_file_ids and not is_window_task:
        # 窗口任务不更新 file_id，避免触发通知
        file_id_string = ';'.join(all_file_ids)
        try:
            # 先读出已有数据
            existing_res = supabase.table("douyin").select("*").eq("aweme_id", aweme_id).limit(1).execute()
            existing_row = existing_res.data[0] if existing_res.data else {}

            # 构建新数据，只更新 file_id / bot_token / liked_by_xxx / saved_by_xxx / failed
            new_data = {
                "aweme_id": aweme_id,
                "file_id": file_id_string,
                "bot_token": actual_bot_token,
                "liked_by_hulu": liked_by_hulu,
                "liked_by_500": liked_by_500,
                "saved_by_hulu": saved_by_hulu,
                "saved_by_500": saved_by_500
            }
            # 如果有failed_reason，同时更新failed字段
            if failed_reason is not None:
                new_data["failed"] = failed_reason
            final_data = sanitize_and_merge(existing_row, new_data)

            # 常规任务，可能是新记录，使用upsert
            result = supabase.table("douyin").upsert(final_data).execute()
            if result.data:
                logger.info(f"成功更新作品 {aweme_id} 的 file_id ({len(all_file_ids)}个媒体) & bot_token & liked/saved 标记")
                db_updated = True
            else:
                logger.error(f"更新作品 {aweme_id} 的 file_id/bot_token/liked/saved 标记失败")
        except Exception as e:
            logger.error(f"更新 file_id/bot_token/liked/saved 标记异常: {str(e)}")
    elif aweme_id and not is_window_task:
        # 窗口任务跳过更新
        logger.info(f"作品 {aweme_id} 没有成功发送的媒体文件，数据库 file_id 未更新。")
        try:
            existing_res = supabase.table("douyin").select("*").eq("aweme_id", aweme_id).limit(1).execute()
            existing_row = existing_res.data[0] if existing_res.data else {}
            new_data = {"aweme_id": aweme_id, "bot_token": actual_bot_token}
            final_data = sanitize_and_merge(existing_row, new_data)
            # 常规任务，可能是新记录，使用upsert
            supabase.table("douyin").upsert(final_data).execute()
            logger.info(f"仅更新了作品 {aweme_id} 的 bot_token => ...{actual_bot_token[-4:]}")
        except Exception as e:
            logger.error(f"更新作品 {aweme_id} 的 bot_token 失败: {str(e)}")

    if DELETE_FILES:
        for file_path in valid_files + music_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
            except Exception as e:
                logger.error(f"删除文件失败: {file_path}, {str(e)}")

    # 窗口任务特殊处理：返回file_id但不更新数据库
    if is_window_task and all_file_ids:
        result_parts = [';'.join(all_file_ids)]
        if music_processed:
            result_parts.append("MUSIC_PROCESSED")
        result_parts.append("WINDOW_TASK")  # 特殊标记
        return ';'.join(result_parts)
    
    result_parts = [';'.join(all_file_ids)]
    if db_updated:
        result_parts.append("UPDATED")
    if music_processed:
        result_parts.append("MUSIC_PROCESSED")
    return ';'.join(result_parts)

# =============== 数据库相关函数 ===============
def is_unavailable_content(resp_data):
    if not resp_data:
        return False
    data = resp_data.get('data', {})
    
    # 典型的作品隐藏或删除结构: aweme_detail 为 null 且存在 filter_detail
    if data.get("aweme_detail") is None and data.get("filter_detail"):
        filter_detail = data.get('filter_detail', {})
        # 检查是否包含常见的错误原因字段
        if (filter_detail.get('detail_msg') or 
            filter_detail.get('filter_reason') or 
            filter_detail.get('notice')):
            return True
    
    return False

def get_unavailable_reason(resp_data):
    data = resp_data.get('data', {})
    filter_detail = data.get('filter_detail', {})
    
    # 组合多个信息字段，提供更完整的错误描述
    detail_msg = filter_detail.get('detail_msg', '')
    filter_reason = filter_detail.get('filter_reason', '')
    notice = filter_detail.get('notice', '')
    
    if detail_msg:
        return detail_msg
    elif notice and filter_reason:
        return f"{notice}: {filter_reason}"
    elif filter_reason:
        return filter_reason
    else:
        return "作品不可用，无具体原因"

def build_unavailable_response(resp_data):
    data = resp_data.get('data', {})
    filter_detail = data.get('filter_detail', {})
    aweme_id = filter_detail.get('aweme_id', '')
    error_message = get_unavailable_reason(resp_data)
    return {
        "data": {
            "aweme_detail": {
                "aweme_id": aweme_id,
                "desc": error_message,
                "create_time": 0,
                "author": {},
                "video": {},
                "images": [],
                "unavailable": True  # 确保明确设置此标记
            },
            "unavailable": True  # 在顶层也设置此标记，确保容易被检测到
        }
    }

async def handle_rate_limit_error(error, aweme_id):
    if error and '429' in str(error):
        logger.warning(f"遇到限流错误: {str(error)}")
        retry_match = re.search(r'retry after (\d+)', str(error), re.IGNORECASE)
        wait_seconds = 15
        if retry_match:
            suggested_wait = int(retry_match.group(1))
            wait_seconds = max(15, int(suggested_wait * 1.5))
        logger.info(f"将等待{wait_seconds}秒后继续处理作品 {aweme_id}")
        await asyncio.sleep(wait_seconds)
        return True
    return False

# 全局端口管理变量（在主函数中初始化）
global_port_manager = {
    'ports': [8081, 8082, 8083],  # 8080端口留给固定端口功能
    'current_index': 0
}

def switch_to_next_port():
    """切换到下一个可用端口"""
    global global_port_manager
    old_port = global_port_manager['ports'][global_port_manager['current_index']]
    global_port_manager['current_index'] = (global_port_manager['current_index'] + 1) % len(global_port_manager['ports'])
    new_port = global_port_manager['ports'][global_port_manager['current_index']]
    logger.warning(f"端口故障转移：从 {old_port} 切换到 {new_port}")
    return new_port

def get_current_port():
    """获取当前使用的端口"""
    global global_port_manager
    return global_port_manager['ports'][global_port_manager['current_index']]

def record_port_failure():
    """记录端口失败并立即切换"""
    global global_port_manager
    current_port = get_current_port()
    logger.warning(f"端口 {current_port} 失败，立即切换到下一个端口")
    return switch_to_next_port()

def record_port_success():
    """记录端口成功"""
    # 端口成功时无需特殊处理，保持当前端口
    pass

async def api_call_with_port_failover(func, *args, max_retries=2, base_wait=15, **kwargs):
    """
    支持端口故障转移的API调用重试包装器
    
    重试策略:
    - 任何错误（包括429）: 立即切换端口重试
    - 超时错误重试时使用更长的超时时间
    
    Args:
        func: 要调用的异步函数
        max_retries: 最大重试次数 (默认2次)
        base_wait: 基础等待时间（秒）
    """
    last_exception = None
    is_timeout_error = False
    
    for attempt in range(max_retries):
        try:
            # 如果是因为超时错误重试，传递retry_timeout参数
            if attempt > 0 and is_timeout_error:
                kwargs['retry_timeout'] = 10.0  # 重试时使用10秒超时
                logger.info(f"因超时错误重试，使用更长的超时时间: 10秒")
            
            return await func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            error_type = type(e).__name__
            error_str = str(e)
            
            # 更详细的错误日志
            if hasattr(e, '__cause__') and e.__cause__:
                cause_type = type(e.__cause__).__name__
                cause_str = str(e.__cause__)
                logger.warning(f"API错误详情 - 类型: {error_type}, 消息: {error_str}, 原因类型: {cause_type}, 原因: {cause_str}")
            else:
                logger.warning(f"API错误详情 - 类型: {error_type}, 消息: {error_str}")
            
            # 检查是否是超时错误
            is_timeout_error = ('timeout' in error_str.lower() or 
                              'ReadTimeout' in error_str or 
                              'ConnectTimeout' in error_str or
                              error_type in ['ConnectTimeout', 'ReadTimeout', 'TimeoutError'])
            
            if is_timeout_error:
                logger.warning(f"检测到超时错误，可能是API响应过慢")
            
            # 对于任何错误（包括429），立即切换端口
            old_port = get_current_port()
            new_port = switch_to_next_port()
            logger.warning(f"端口切换: {old_port} → {new_port} (第{attempt + 1}次尝试)")
            
            # 如果还有重试机会，继续
            if attempt < max_retries - 1:
                await asyncio.sleep(1)  # 短暂等待
                continue
            else:
                # 已达到最大重试次数
                logger.error(f"API调用失败，已尝试所有端口 - 最终错误类型: {error_type}, 消息: {error_str}")
                raise
    
    # 达到最大重试次数，抛出最后的异常
    logger.error(f"API调用达到最大重试次数 {max_retries}，仍然失败")
    raise last_exception

async def fetch_one_video_api(aweme_id) -> dict:
    async def _fetch():
        base_url = f"{API_BASE_URL}/api/douyin/web/fetch_one_video"
        url = f"{base_url}?aweme_id={aweme_id}"
        async with httpx.AsyncClient(timeout=5.0) as client:
            r = await client.get(url)
            if r.status_code == 400:
                logger.warning(f"fetch_one_video_api: HTTP 400 (aweme_id={aweme_id})，可能Cookie失效或作品不可访问")
                return {}
            if r.status_code == 429:
                raise httpx.HTTPStatusError("429 Too Many Requests", request=r.request, response=r)
            if not r.is_success:
                r.raise_for_status()
            json_data = r.json()
            
            # 检查作品是否不可用 - 可能是被删除或设为私密
            if is_unavailable_content(json_data):
                logger.warning(f"作品 {aweme_id} 存在但不可用: {get_unavailable_reason(json_data)}")
                return build_unavailable_response(json_data)
            
            # 直接检查是否有 filter_detail 和缺少 aweme_detail，这通常表示作品不可用
            data = json_data.get('data', {})
            if not data.get('aweme_detail') and data.get('filter_detail'):
                logger.warning(f"作品 {aweme_id} 存在但不可用: {get_unavailable_reason(json_data)}")
                return build_unavailable_response(json_data)
                
            return json_data
    
    try:
        # 使用重试包装器
        return await api_call_with_port_failover(_fetch)
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTPStatusError: {str(e)}")
        return {}
    except Exception as e:
        logger.error(f"fetch_one_video_api error: {str(e)}")
        return {}

async def fetch_user_profile_api(sec_uid: str, port=None) -> dict:
    """支持端口故障转移的用户资料获取API"""
    
    async def _fetch(retry_timeout=None):
        current_port = port if port else get_current_port()
        url = f"http://localhost:{current_port}/api/douyin/web/handler_user_profile"
        logger.info(f"开始获取用户资料，sec_uid={sec_uid}，使用端口: {current_port}")
        
        # 使用retry_timeout参数或默认5秒
        timeout_value = retry_timeout if retry_timeout else 5.0
        async with httpx.AsyncClient(timeout=timeout_value) as client:
            try:
                r = await client.get(url, params={"sec_user_id": sec_uid})
            except httpx.ConnectTimeout as e:
                logger.error(f"连接超时: {str(e)}")
                raise Exception(f"ConnectTimeout: 无法连接到 {current_port} 端口")
            except httpx.ReadTimeout as e:
                logger.error(f"读取超时: {str(e)}")
                raise Exception(f"ReadTimeout: API响应超时 ({current_port} 端口)")
            except httpx.HTTPError as e:
                logger.error(f"HTTP错误: {type(e).__name__}: {str(e)}")
                raise Exception(f"HTTPError: {type(e).__name__} - {str(e)}")
                
            if r.status_code == 400:
                logger.warning(f"fetch_user_profile_api: 400错误 (Cookie可能失效)，sec_uid={sec_uid}")
                return {}
            if r.status_code == 429:
                raise Exception("429 Too Many Requests")
            if not r.is_success:
                raise Exception(f"HTTP error! status: {r.status_code}")
            result = r.json()
            logger.info(f"成功获取用户资料: {sec_uid}")
            return result
    
    try:
        if port:
            # 如果指定了端口，直接调用，不使用端口故障转移
            return await _fetch()
        else:
            # 使用端口故障转移重试包装器
            return await api_call_with_port_failover(_fetch)
    except Exception as e:
        logger.error(f"获取用户资料失败: {str(e)}")
        return {}

def build_live_payload(user: dict) -> Optional[dict]:
    """
    修改：完整保留接口返回的 room_data，包括清晰度、编码信息等所有字段。
    如果 live_status=1，且 room_data 非空，我们将其原样解析并返回。
    该函数同时支持test.json和test2.json的数据结构。
    """
    logger.info(f"build_live_payload: live_status={user.get('live_status')}")
    if user.get("live_status") != 1:
        return None

    raw = user.get("room_data")
    logger.info(f"build_live_payload: room_data类型={type(raw)}, 是否为空={raw is None}")
    if not raw:
        return None

    # 尝试反序列化为 dict
    room_dict = None
    if isinstance(raw, str):
        try:
            room_dict = json.loads(raw)
            logger.info("build_live_payload: 成功将字符串解析为JSON")
            
            # 尝试确定JSON结构类型，并记录关键字段
            structure_type = "未知"
            if isinstance(room_dict, dict):
                # 检测关键字段以识别结构类型
                if "stream_url" in room_dict and "owner" in room_dict:
                    structure_type = "test.json类型"
                    logger.info(f"检测到test.json类型结构，包含stream_url和owner字段")
                    
                    # 提取web_rid (如果存在)
                    owner_info = room_dict.get("owner", {})
                    if isinstance(owner_info, dict) and "web_rid" in owner_info:
                        web_rid = owner_info.get("web_rid")
                        logger.info(f"从room_data中提取到web_rid: {web_rid}")
                
                # 记录其他可能有用的字段
                if "status" in room_dict:
                    logger.info(f"room_data包含状态码: {room_dict.get('status')}")
                if "user_count" in room_dict:
                    logger.info(f"room_data包含观众数量: {room_dict.get('user_count')}")
                
                logger.info(f"room_data结构类型: {structure_type}, 顶级字段: {list(room_dict.keys())}")
            
        except Exception as e:
            logger.warning(f"room_data 反序列化失败: {e}")
            # 即使解析失败，也返回原始数据
            return {"room_data_raw": raw}
    elif isinstance(raw, dict):
        room_dict = raw
        logger.info("build_live_payload: room_data已经是dict类型")
    else:
        logger.warning(f"build_live_payload: room_data类型不支持: {type(raw)}")
        return None

    result = {
        "full_data": room_dict,
        "room_data_raw": raw
    }
    logger.info(f"build_live_payload: 返回数据结构={json.dumps(result, ensure_ascii=False)[:200]}...")
    return result

def map_user_json_to_db(u: dict, live_payload: Optional[dict]) -> dict:
    field_map = {
        "uid": "uid",
        "sec_uid": "sec_uid",
        "short_id": "short_id",
        "unique_id": "unique_id",
        "nickname": "nickname",
        "signature": "signature",
        "user_age": "user_age",
        "follower_count": "follower_count",
        "following_count": "following_count",
        "total_favorited": "total_favorited",
        "favoriting_count": "favoriting_count",
        "status": "status",
        "verification_type": "verification_type",
        "user_canceled": "user_canceled",
        "mate_add_permission": "mate_add_permission",
        "custom_verify": "custom_verify",
        "enterprise_verify_reason": "enterprise_verify_reason",
        "prevent_download": "prevent_download",
        "contacts_status": "contacts_status",
        "cover_url": "cover_url",
        "white_cover_url": "white_cover_url",
        "cover_colour": "cover_colour",
        "commerce_info": "commerce_info",
        "commerce_user_info": "commerce_user_info",
        "share_info": "share_info",
        "personal_tag_list": "personal_tag_list",
        "tab_settings": "tab_settings",
        "live_status": "live_status",
        "room_id": "room_id",
        "room_id_str": "room_id_str",
        "live_user_count": "live_user_count",
        "gender": "gender",
        "ip_location": "ip_location",
        "aweme_count": "aweme_count",
        "max_follower_count": "max_follower_count",
        "mix_count": "mix_count",
        "series_count": "series_count",
        "dongtai_count": "dongtai_count",
        "forward_count": "forward_count",
        "public_collects_count": "public_collects_count",
        "favorite_permission": "favorite_permission",
        "follower_status": "follower_status",
        "follow_status": "follow_status",
        "country": "country",
        "province": "province",
        "district": "district",
        "commerce_user_level": "commerce_user_level",
        "with_commerce_entry": "with_commerce_entry",
        "with_fusion_shop_entry": "with_fusion_shop_entry",
        "with_commerce_enterprise_tab_entry": "with_commerce_enterprise_tab_entry"
    }
    row = {db: u.get(js) for db, js in field_map.items() if u.get(js) is not None}
    row["avatar_thumb_uri"]   = u.get("avatar_thumb", {}).get("uri")
    row["avatar_thumb_url"]   = (u.get("avatar_thumb", {}).get("url_list") or [None])[0] if u.get("avatar_thumb") else None
    row["avatar_168x168_url"] = (u.get("avatar_168x168", {}).get("url_list") or [None])[0] if u.get("avatar_168x168") else None
    row["avatar_300x300_url"] = (u.get("avatar_300x300", {}).get("url_list") or [None])[0] if u.get("avatar_300x300") else None
    row["avatar_larger_url"]  = (u.get("avatar_larger", {}).get("url_list")  or [None])[0] if u.get("avatar_larger") else None
    row["avatar_medium_url"]  = (u.get("avatar_medium", {}).get("url_list")  or [None])[0] if u.get("avatar_medium") else None
    row["cover_url"]          = json.dumps(u.get("cover_url") or None)
    row["white_cover_url"]    = json.dumps(u.get("white_cover_url") or None)
    row["cover_colour"]       = u.get("cover_colour")

    for k in ("commerce_info", "commerce_user_info", "share_info", "personal_tag_list", "tab_settings"):
        row[k] = u.get(k)

    # 根据用户需求转换 live_status
    api_live_status_value = u.get("live_status")
    row["live_status"] = True if api_live_status_value == 1 else False

    row["room_id"]     = u.get("room_id") or None
    row["room_id_str"] = None if u.get("room_id_str") in (None, "0") else u.get("room_id_str")
    
    # Extract web_rid from u.get("room_data") and construct new_url
    # u.get("room_data") is expected to be a JSON string based on API responses like test.json
    raw_room_data_for_new_url = u.get("room_data")
    web_rid_for_new_url = None

    if isinstance(raw_room_data_for_new_url, str) and raw_room_data_for_new_url:
        try:
            parsed_room_data = json.loads(raw_room_data_for_new_url)
            if isinstance(parsed_room_data, dict):
                # 尝试标准路径: owner.web_rid
                owner_info = parsed_room_data.get("owner")
                if isinstance(owner_info, dict):
                    web_rid_for_new_url = owner_info.get("web_rid")
                    if web_rid_for_new_url:
                        logger.info(f"从标准路径提取到web_rid '{web_rid_for_new_url}' (用户 {u.get('uid')})。")
                
                # 如果标准路径未找到，尝试其他可能的结构
                if not web_rid_for_new_url:
                    # 查找可能存在的其他结构
                    if "stream_url" in parsed_room_data and "owner" in parsed_room_data:
                        logger.info(f"检测到包含stream_url的结构 (用户 {u.get('uid')})")
                        # 尝试直接从这一层获取web_rid
                        if parsed_room_data.get("owner", {}).get("web_rid"):
                            web_rid_for_new_url = parsed_room_data.get("owner", {}).get("web_rid")
                            logger.info(f"从备用路径提取到web_rid '{web_rid_for_new_url}' (用户 {u.get('uid')})。")
                    
                    # 检查可能的不同结构
                    if not web_rid_for_new_url and "live_room_mode" in parsed_room_data:
                        logger.info(f"检测到包含live_room_mode的结构 (用户 {u.get('uid')})")
                        if parsed_room_data.get("owner", {}).get("web_rid"):
                            web_rid_for_new_url = parsed_room_data.get("owner", {}).get("web_rid")
                            logger.info(f"从live_room_mode结构提取到web_rid '{web_rid_for_new_url}' (用户 {u.get('uid')})。")
                    
                    # 深度查找web_rid (test.json和test2.json结构都支持)
                    if not web_rid_for_new_url:
                        logger.info(f"尝试深度查找web_rid (用户 {u.get('uid')})")
                        # 尝试递归遍历字典查找web_rid
                        def find_web_rid(data, path=""):
                            if not isinstance(data, dict):
                                return None
                            
                            # 直接检查当前层是否有owner.web_rid
                            if "owner" in data and isinstance(data["owner"], dict) and "web_rid" in data["owner"]:
                                logger.info(f"在路径 {path}.owner.web_rid 找到web_rid")
                                return data["owner"]["web_rid"]
                            
                            # 递归检查所有字典类型的值
                            for key, value in data.items():
                                if isinstance(value, dict):
                                    new_path = f"{path}.{key}" if path else key
                                    result = find_web_rid(value, new_path)
                                    if result:
                                        return result
                            return None
                        
                        # 尝试深度查找
                        deep_web_rid = find_web_rid(parsed_room_data)
                        if deep_web_rid:
                            web_rid_for_new_url = deep_web_rid
                            logger.info(f"通过深度查找找到web_rid '{web_rid_for_new_url}' (用户 {u.get('uid')})。")
        except json.JSONDecodeError as e:
            logger.warning(f"解析用户 {u.get('uid')} 的room_data字符串失败: {e}. 数据片段: {raw_room_data_for_new_url[:200]}")
        except Exception as e:
            logger.error(f"解析用户 {u.get('uid')} 的room_data时发生意外错误: {e}. 数据片段: {raw_room_data_for_new_url[:200]}")

    if web_rid_for_new_url:
        new_url = f"https://live.douyin.com/{web_rid_for_new_url}"
        row["new_url"] = new_url
        
        # 检查anchors表是否已有相同new_url的记录
        anchor_id = None
        try:
            anchor_res = supabase.table("anchors").select("*").eq("new_url", new_url).limit(1).execute()
            if anchor_res.data:
                # 记录已存在，获取anchor_id
                anchor_id = anchor_res.data[0].get('anchor_id')
                logger.info(f"找到已存在的anchor记录，anchor_id: {anchor_id}, new_url: {new_url}")
            else:
                # 在anchors表中不存在该记录，创建新记录
                anchor_data = {
                    "new_url": new_url
                }
                insert_res = supabase.table("anchors").insert(anchor_data).execute()
                
                # 尝试获取新创建记录的anchor_id
                max_retries = 3
                for retry in range(max_retries):
                    try:
                        # 等待一小段时间确保记录已创建
                        if retry > 0:
                            time.sleep(0.5)
                        
                        # 重新查询获取anchor_id
                        new_anchor_res = supabase.table("anchors").select("anchor_id").eq("new_url", new_url).limit(1).execute()
                        if new_anchor_res.data:
                            anchor_id = new_anchor_res.data[0].get('anchor_id')
                            logger.info(f"成功获取新创建anchor记录的anchor_id: {anchor_id}, new_url: {new_url}")
                            break
                    except Exception as retry_e:
                        logger.warning(f"第{retry+1}次尝试获取anchor_id失败: {retry_e}")
                        if retry == max_retries - 1:
                            logger.error(f"最终无法获取anchor_id for new_url: {new_url}")
                
                if anchor_id:
                    logger.info(f"为用户 {u.get('uid')} ({u.get('nickname', '')}) 在anchors表中创建了记录: anchor_id={anchor_id}, new_url={new_url}")
                else:
                    logger.warning(f"创建anchor记录成功但未能获取anchor_id, new_url: {new_url}")
                    
            # 将anchor_id添加到row中，后续会更新到douyin_user表
            if anchor_id:
                row["anchor_id"] = anchor_id
                
        except Exception as e:
            logger.error(f"检查或插入anchors表记录时出错: {e}")
    # If web_rid_for_new_url is None, row["new_url"] will not be set here.
    # sanitize_and_merge will then correctly preserve an existing new_url if present,
    # or leave it as not present if it was never set and not found now.
    
    if live_payload:
        logger.info(f"map_user_json_to_db: live_payload类型={type(live_payload)}")
        full_data = live_payload.get("full_data", {})
        logger.info(f"map_user_json_to_db: full_data类型={type(full_data)}")
        row["room_data"] = json.dumps(full_data, ensure_ascii=False)
        logger.info(f"map_user_json_to_db: room_data已设置，长度={len(row['room_data'])}")

        # 存储直播人数
        if "user_count" in full_data:
            row["live_user_count"] = full_data.get("user_count")
            logger.info(f"map_user_json_to_db: 直播人数={row['live_user_count']}")
        
    row["updated_at"] = datetime.utcnow().isoformat() + "Z"
    return row

def upsert_live_info_to_db(user: dict, live_payload: Optional[dict]) -> None:
    row = map_user_json_to_db(user, live_payload)
    uid = user.get("uid")
    nickname = user.get("nickname", "未知用户")

    logger.info(f"更新作者 {nickname}({uid}) 直播数据 => upsert(douyin_user)")
    
    logger.info(f"用户信息详情 {nickname}({uid}):")
    logger.info(f"基本信息: uid={row.get('uid')}, sec_uid={row.get('sec_uid')}, nickname={row.get('nickname')}, signature={row.get('signature')}")
    logger.info(f"直播状态: live_status={row.get('live_status')}, room_id={row.get('room_id')}, room_id_str={row.get('room_id_str')}")
    logger.info(f"粉丝数据: follower_count={row.get('follower_count')}, following_count={row.get('following_count')}")
    logger.info(f"内容数据: aweme_count={row.get('aweme_count')}, total_favorited={row.get('total_favorited')}")

    if row.get('live_status') == 1:
        logger.info(f"直播数据: live_user_count={row.get('live_user_count', '未知')}")
        if 'room_data' in row and row['room_data']:
            room_data_str = row['room_data']
            logger.info(f"直播详情(长度={len(room_data_str)}字节):")
            try:
                room_data_json = json.loads(room_data_str)
                if 'stream_url' in room_data_json:
                    stream_url = room_data_json['stream_url']
                    logger.info(f"  - 清晰度选项: {stream_url.get('candidate_resolution', [])}")
                    logger.info(f"  - 默认清晰度: {stream_url.get('default_resolution')}")
                    if 'flv_pull_url' in stream_url:
                        logger.info(f"  - FLV直播地址数量: {len(stream_url['flv_pull_url'])}")
                if 'user_count' in room_data_json:
                    logger.info(f"  - 观看人数: {room_data_json['user_count']}")
                if 'status' in room_data_json:
                    logger.info(f"  - 直播状态码: {room_data_json['status']}")
            except:
                logger.info(f"  无法解析room_data为JSON，前200字符: {room_data_str[:200]}...")
    
    existing_res = supabase.table("douyin_user").select("*").eq("uid", uid).limit(1).execute()
    existing_row = existing_res.data[0] if existing_res.data else {}

    if existing_row:
        existing_columns = existing_row.keys()
        filtered_row = {k: v for k, v in row.items() if k in existing_columns}
        row = filtered_row

    final_data = sanitize_and_merge(existing_row, row)
    
    try:
        supabase.table("douyin_user").upsert(final_data).execute()
        logger.info(f"成功更新用户 {uid} ({nickname}) 的直播数据")
    except Exception as e:
        logger.error(f"更新用户 {uid} ({nickname}) 的直播数据失败: {e}")
        logger.error(f"尝试更新的字段: {list(final_data.keys())}")

async def save_douyin_to_database(parsed_data, is_full_scan=False, failed_reason=None,
                                  liked_by_hulu=False, liked_by_500=False,
                                  saved_by_hulu=False, saved_by_500=False):
    try:
        base_info = parsed_data.get('base_info', {})
        author_info = parsed_data.get('author_info', {})
        location_info = parsed_data.get('location_info', {})
        music_info = parsed_data.get('music_info', {})
        media_info = parsed_data.get('media_info', {})

        aweme_id = base_info.get('aweme_id', "")
        if not aweme_id:
            logger.error("保存数据库失败: 缺少作品ID")
            return {"status": "error", "message": "Missing aweme_id"}

        create_time = base_info.get('create_time') or None
        data = {
            "aweme_id": aweme_id,
            "description": base_info.get('desc', None),
            "create_time": create_time,
            "nickname": author_info.get('nickname'),
            "uid": author_info.get('uid'),
            "sec_uid": author_info.get('sec_uid'),
            "unique_id": author_info.get('unique_id'),
            "follower_count": author_info.get('follower_count', 0),
            "total_favorited": author_info.get('total_favorited', 0),
            "province": location_info.get('province'),
            "city": location_info.get('city'),
            "music_title": music_info.get('title'),
            "music_author": music_info.get('author'),
            "music_play_url": music_info.get('play_url'),
            "cover_url": media_info.get('cover_url'),
            "media_play_url": media_info.get('play_url'),
            "duration": media_info.get('duration', 0),
            "bot_token": BOT_TOKEN,
            "failed": failed_reason,
            "liked_by_hulu": liked_by_hulu,
            "liked_by_500": liked_by_500,
            "saved_by_hulu": saved_by_hulu,
            "saved_by_500": saved_by_500
        }

        existing_res = supabase.table("douyin").select("*").eq("aweme_id", aweme_id).limit(1).execute()
        existing_row = existing_res.data[0] if existing_res.data else {}

        final_data = sanitize_and_merge(existing_row, data)
        result = supabase.table("douyin").upsert(final_data).execute()

        if failed_reason:
            logger.info(f"成功记录作品 {aweme_id} 的失败原因 '{failed_reason}'")
        else:
            logger.info(f"成功保存/更新作品 {aweme_id} 到数据库")

        return {"status": "saved"}
    except Exception as e:
        error_detail = str(e)
        logger.error(f"保存数据库异常: {error_detail}")
        return {"status": "error", "message": error_detail}

async def save_douyin_user_to_database(user_data):
    """
    保存抖音用户数据到数据库
    """
    try:
        uid = user_data.get("uid")
        if not uid:
            logger.error("保存用户数据失败: 缺少必要字段uid")
            return False

        logger.info(f"保存用户数据: {user_data.get('nickname', '未知')} (uid: {uid})")
        
        existing_res = supabase.table("douyin_user").select("*").eq("uid", uid).limit(1).execute()
        existing_row = existing_res.data[0] if existing_res.data else {}
        
        final_data = sanitize_and_merge(existing_row, user_data)
        
        logger.info(f"用户完整信息 {user_data.get('nickname', '未知')}({uid}):\n{json.dumps(final_data, ensure_ascii=False, indent=2)}")
        
        supabase.table("douyin_user").upsert(final_data).execute()
        
        logger.info(f"成功保存用户 {uid} ({user_data.get('nickname', '未知')}) 到数据库")
        return True
    except Exception as e:
        logger.error(f"保存用户数据到数据库异常: {str(e)}")
        return False

# =============== 获取用户作品列表 ===============
async def fetch_user_post_videos_api(sec_user_id, max_cursor=0, count=40, port=None):
    """支持端口故障转移的用户作品获取API"""
    
    async def _fetch(retry_timeout=None):
        current_port = port if port else get_current_port()
        base_url = f"http://localhost:{current_port}/api/douyin/web/fetch_user_post_videos"
        params = {
            "sec_user_id": sec_user_id,
            "max_cursor": max_cursor,
            "count": count
        }
        url = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
        logger.info(f"获取用户作品API请求: {url}")
        
        # 使用retry_timeout参数或默认7秒
        timeout_value = retry_timeout if retry_timeout else 7.0
        async with httpx.AsyncClient(timeout=timeout_value) as client:
            try:
                start_time = time.time()
                response = await client.get(url)
                elapsed = time.time() - start_time
                logger.info(f"API响应耗时: {elapsed:.2f}秒")
            except httpx.ConnectTimeout as e:
                logger.error(f"连接超时: {str(e)}")
                raise Exception(f"ConnectTimeout: 无法连接到 {current_port} 端口")
            except httpx.ReadTimeout as e:
                logger.error(f"读取超时: {str(e)}")
                raise Exception(f"ReadTimeout: API响应超时 ({current_port} 端口)")
            except httpx.HTTPError as e:
                logger.error(f"HTTP错误: {type(e).__name__}: {str(e)}")
                raise Exception(f"HTTPError: {type(e).__name__} - {str(e)}")
            
            if response.status_code == 400:
                logger.warning(f"fetch_user_post_videos_api: 状态码400 (sec_user_id={sec_user_id}, Cookie可能失效)")
                logger.debug(f"API错误响应内容: {response.text[:500]}")
                return {}
            
            if response.status_code == 429:
                raise Exception("429 Too Many Requests")
                
            if not response.is_success:
                logger.error(f"fetch_user_post_videos_api: HTTP错误! 状态码: {response.status_code}, 内容: {response.text[:200]}...")
                raise Exception(f"HTTP error! status: {response.status_code}")
                
            try:
                json_data = response.json()
                # 记录响应的大小和结构
                logger.debug(f"API响应大小: {len(response.content)} 字节")
                data_field = json_data.get('data')
                if data_field is None:
                    logger.warning(f"API响应中没有'data'字段")
                else:
                    if isinstance(data_field, dict):
                        logger.debug(f"data字段包含的键: {list(data_field.keys())}")
                        aweme_list = data_field.get('aweme_list')
                        if aweme_list is None:
                            logger.warning(f"data字段中没有'aweme_list'键")
                        elif not isinstance(aweme_list, list):
                            logger.warning(f"'aweme_list'不是列表类型，而是 {type(aweme_list).__name__}")
                        else:
                            logger.debug(f"获取到 {len(aweme_list)} 个作品项目")
                    else:
                        logger.warning(f"data字段不是字典类型，而是 {type(data_field).__name__}")
                return json_data
            except json.JSONDecodeError as jde:
                logger.error(f"无法解析API响应为JSON: {jde}, 原始响应: {response.text[:500]}")
                return {}
    
    try:
        if port:
            # 如果指定了端口，直接调用，不使用端口故障转移
            return await _fetch()
        else:
            # 使用端口故障转移重试包装器
            return await api_call_with_port_failover(_fetch)
    except Exception as e:
        logger.error(f"获取用户作品失败: {str(e)} (sec_user_id={sec_user_id})")
        return {}

async def fetch_user_post_videos_from_tikhub(sec_user_id, max_cursor=0, count=40):
    try:
        base_url = "https://beta.tikhub.io/api/v1/douyin/app/v3/fetch_user_post_videos"
        params = {
            "sec_user_id": sec_user_id,
            "max_cursor": max_cursor,
            "count": count
        }
        url = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
        logger.info(f"TikHub API请求: {url}")
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            start_time = time.time()
            response = await client.get(
                url,
                headers={
                    'accept': 'application/json',
                    'Authorization': f'Bearer {TIKHUB_API_TOKEN}'
                }
            )
            elapsed = time.time() - start_time
            logger.info(f"TikHub API响应耗时: {elapsed:.2f}秒")
            
            if not response.is_success:
                logger.error(f"TikHub API HTTP错误! 状态码: {response.status_code}, 内容: {response.text[:200]}...")
                raise Exception(f"TikHub API HTTP error! status: {response.status_code}")
            
            try:
                json_data = response.json()
                # 记录响应的大小和结构
                logger.debug(f"TikHub API响应大小: {len(response.content)} 字节")
                data_field = json_data.get('data')
                if data_field is None:
                    logger.warning(f"TikHub API响应中没有'data'字段")
                else:
                    if isinstance(data_field, dict):
                        logger.debug(f"TikHub data字段包含的键: {list(data_field.keys())}")
                        aweme_list = data_field.get('aweme_list')
                        if aweme_list is None:
                            logger.warning(f"TikHub data字段中没有'aweme_list'键")
                        elif not isinstance(aweme_list, list):
                            logger.warning(f"TikHub 'aweme_list'不是列表类型，而是 {type(aweme_list).__name__}")
                        else:
                            logger.debug(f"TikHub API获取到 {len(aweme_list)} 个作品项目")
                    else:
                        logger.warning(f"TikHub data字段不是字典类型，而是 {type(data_field).__name__}")
                return json_data
            except json.JSONDecodeError as jde:
                logger.error(f"无法解析TikHub API响应为JSON: {jde}, 原始响应: {response.text[:500]}")
                return {}
    except httpx.TimeoutException as te:
        logger.error(f"TikHub API请求超时: {te} (sec_user_id={sec_user_id})")
        return {}
    except httpx.HTTPError as he:
        logger.error(f"TikHub API网络错误: {he} (sec_user_id={sec_user_id})")
        return {}
    except Exception as e:
        logger.error(f"TikHub API未预期错误: {str(e)} (sec_user_id={sec_user_id})", exc_info=True)
        return {}

async def fetch_collected_videos_api(cookie: str, user_label: str):
    """
    获取指定用户的收藏视频列表 (仅第一页)。
    """
    if not cookie:
        logger.error(f'未提供 Cookie ({user_label})，无法获取收藏视频')
        return []
    
    url = f"{TIKHUB_API_BASE_URL}/fetch_user_collection_videos" # Use collection endpoint
    headers = {
        'accept': 'application/json',
        'Authorization': f'Bearer {TIKHUB_API_TOKEN}',
        'Content-Type': 'application/json'
    }
    video_ids = []
    id_set = set()
    duplicate_count = 0
    max_cursor = 0
    has_more = True # Keep track if API indicates more pages, even if we only fetch one
    page_count = 0
    counts_per_page = 20 # 设置每页数量为 20
    max_retries = 2 # 首次尝试 + 2次重试

    logger.info(f"开始获取 [{user_label}] 的收藏视频ID列表 (仅第一页)...")
    # 修改循环条件，只获取第一页
    while page_count < 1:
        page_count += 1
        logger.info(f"正在获取 [{user_label}] 第 {page_count} 页收藏数据 (每页 {counts_per_page} 条)...")
        
        # --- 构造 Payload --- Modify payload based on page number
        # 第一页仅发送 cookie (根据之前的发现)
        payload = {
            "cookie": cookie,
            "counts": counts_per_page # 确保第一页也带上 counts
        }
        # 移除 payload 日志
        # logger.info(f"发送第 {page_count} 页请求，Payload: {json.dumps(payload)}")
        # ------
        
        for attempt in range(max_retries + 1):
            response = None
            try:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.post(url, headers=headers, json=payload)
                
                # 检查 HTTP 状态码
                if not response.is_success:
                    logger.error(f"TikHub收藏API请求失败 (第 {page_count} 页, 尝试 {attempt + 1}/{max_retries + 1}): {response.status_code} - {response.text}")
                    if attempt < max_retries:
                        wait_seconds = 2 ** (attempt + 1) # 指数退避
                        logger.warning(f"将在 {wait_seconds} 秒后重试...")
                        await asyncio.sleep(wait_seconds)
                        continue # 继续下一次重试
                    else:
                        logger.error(f"达到最大重试次数，停止获取 [{user_label}] 的收藏数据。")
                        # Loop will terminate anyway due to page_count condition
                        break # 退出重试循环
                
                # 解析 JSON 并检查内部 code
                try:
                    data = response.json()
                    if data.get('code') != 200:
                        logger.error(f"TikHub收藏API返回错误 (第 {page_count} 页, 尝试 {attempt + 1}/{max_retries + 1}): {json.dumps(data)}")
                        if attempt < max_retries:
                            wait_seconds = 2 ** (attempt + 1)
                            logger.warning(f"将在 {wait_seconds} 秒后重试...")
                            await asyncio.sleep(wait_seconds)
                            continue # 继续下一次重试
                        else:
                            logger.error(f"达到最大重试次数，停止获取 [{user_label}] 的收藏数据。")
                            # Loop will terminate anyway due to page_count condition
                            break # 退出重试循环
                except json.JSONDecodeError:
                     logger.error(f"无法解析API响应为JSON (第 {page_count} 页, 尝试 {attempt + 1}/{max_retries + 1})。响应内容: {response.text[:200]}...", exc_info=True)
                     if attempt < max_retries:
                            wait_seconds = 2 ** (attempt + 1)
                            logger.warning(f"将在 {wait_seconds} 秒后重试...")
                            await asyncio.sleep(wait_seconds)
                            continue # 继续下一次重试
                     else:
                         logger.error(f"达到最大重试次数，停止获取 [{user_label}] 的收藏数据。")
                         # Loop will terminate anyway due to page_count condition
                         break # 退出重试循环

                # --- 请求成功，处理数据 ---
                aweme_list = data.get('data', {}).get('aweme_list') or []
                
                if not aweme_list and page_count == 1: # 仅在第一页数据为空时打印信息
                     logger.info(f"[{user_label}] 第 {page_count} 页收藏数据为空或API未返回列表。")
                     # 即使为空，也认为本次请求成功，由 has_more 决定是否继续

                page_unique_count = 0
                page_duplicate_count = 0
                for video in aweme_list: # Now safe to iterate
                    aweme_id = video.get('aweme_id')
                    if not aweme_id:
                        continue
                    if aweme_id in id_set:
                        page_duplicate_count += 1
                        duplicate_count += 1
                    else:
                        id_set.add(aweme_id)
                        video_ids.append(aweme_id)
                        page_unique_count += 1
                logger.info(f"本页获取到 {len(aweme_list)} 个收藏作品，其中新ID {page_unique_count} 个，重复ID {page_duplicate_count} 个")

                has_more_api = data.get('data', {}).get('has_more') == 1
                if has_more_api:
                    max_cursor = data.get('data', {}).get('max_cursor', 0)
                    # await asyncio.sleep(1) # No delay needed as we only fetch one page
                else:
                    logger.info("API 指示没有更多收藏数据")
                
                has_more = has_more_api # Update has_more based on API response for the first page
                
                break # 成功处理，退出重试循环

            except (httpx.ReadTimeout, httpx.ConnectTimeout, httpx.RequestError) as net_error:
                logger.error(f"请求收藏数据时发生网络错误 (第 {page_count} 页, 尝试 {attempt + 1}/{max_retries + 1}): {type(net_error).__name__} - {net_error}")
                if attempt < max_retries:
                    wait_seconds = 2 ** (attempt + 1)
                    logger.warning(f"将在 {wait_seconds} 秒后重试...")
                    await asyncio.sleep(wait_seconds)
                    continue # 继续下一次重试
                else:
                    logger.error(f"达到最大重试次数，停止获取 [{user_label}] 的收藏数据。")
                    # Loop will terminate anyway due to page_count condition
                    break # 退出重试循环
            except Exception as error:
                logger.error(f"处理收藏数据时发生未知错误 (第 {page_count} 页, 尝试 {attempt + 1}/{max_retries + 1}): {error}", exc_info=True)
                if attempt < max_retries:
                    wait_seconds = 2 ** (attempt + 1)
                    logger.warning(f"将在 {wait_seconds} 秒后重试...")
                    await asyncio.sleep(wait_seconds)
                    continue # 继续下一次重试
                else:
                    logger.error(f"达到最大重试次数，停止获取 [{user_label}] 的收藏数据。")
                    # Loop will terminate anyway due to page_count condition
                    break # 退出重试循环
            
    if duplicate_count > 0:
        logger.warning(f"收藏API总共返回了 {duplicate_count} 个重复ID，已过滤")
        
    # 修改日志消息，明确只获取了第一页
    logger.info(f"收藏数据总共获取到 {len(video_ids)} 个视频ID (去重后)，来自第一页数据")
    
    # 如果第一页就没有获取到任何数据，说明Cookie可能已失效
    if page_count == 1 and len(video_ids) == 0:
        logger.error(f"[{user_label}] 第一页收藏数据为空，Cookie可能已失效，更新douyinlikesfailure标记")
        try:
            # 更新does表的douyinlikesfailure字段
            supabase.table("does").update({"douyinlikesfailure": True}).eq("id", 1).execute()
            logger.info(f"已将douyinlikesfailure标记设置为true，因为{user_label}的Cookie可能失效")
        except Exception as e:
            logger.error(f"更新douyinlikesfailure标记失败: {str(e)}")
    
    return video_ids

async def fetch_liked_videos_api(cookie: str, user_label: str, is_full_scan=False):
    if not cookie:
        logger.error(f'未提供 Cookie ({user_label})，无法获取点赞视频')
        return []
    url = f"{TIKHUB_API_BASE_URL}/fetch_user_like_videos"
    headers = {
        'accept': 'application/json',
        'Authorization': f'Bearer {TIKHUB_API_TOKEN}',
        'Content-Type': 'application/json'
    }
    video_ids = []
    id_set = set()
    duplicate_count = 0
    max_cursor = 0
    has_more = True
    page_count = 0
    counts_per_page = 80

    logger.info(f"开始获取 [{user_label}] 的点赞视频ID列表 ({'全量' if is_full_scan else f'前 {LIKED_VIDEOS_PAGES} 页'})...")
    while has_more and (is_full_scan or page_count < LIKED_VIDEOS_PAGES):
        page_count += 1
        logger.info(f"正在获取 [{user_label}] 第 {page_count} 页点赞数据 (每页 {counts_per_page} 条)...")
        payload = {
            "sec_user_id": "string", # Note: Liked API seems to require sec_user_id, even if it's just "string"
            "max_cursor": max_cursor,
            "counts": counts_per_page,
            "cookie": cookie
        }
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(url, headers=headers, json=payload)
            if not response.is_success:
                logger.error(f"TikHub点赞API请求失败: {response.status_code} - {response.text}")
                break
            data = response.json()
            if data.get('code') != 200:
                logger.error(f"TikHub点赞API返回错误: {json.dumps(data)}")
                break

            # Ensure aweme_list is always a list, even if API returns null
            aweme_list = data.get('data', {}).get('aweme_list') or [] 

            if not aweme_list:
                logger.info(f"[{user_label}] 第 {page_count} 页点赞数据为空或API未返回列表。")
            
            page_unique_count = 0
            page_duplicate_count = 0
            for video in aweme_list: # Now safe to iterate
                aweme_id = video.get('aweme_id')
                if not aweme_id:
                    continue
                if aweme_id in id_set:
                    page_duplicate_count += 1
                    duplicate_count += 1
                else:
                    id_set.add(aweme_id)
                    video_ids.append(aweme_id)
                    page_unique_count += 1
            logger.info(f"本页获取到 {len(aweme_list)} 个作品，其中新ID {page_unique_count} 个，重复ID {page_duplicate_count} 个")

            has_more = data.get('data', {}).get('has_more') == 1
            if has_more and (is_full_scan or page_count < LIKED_VIDEOS_PAGES):
                max_cursor = data.get('data', {}).get('max_cursor', 0)
                await asyncio.sleep(1)
            else:
                if not is_full_scan:
                    logger.info(f"已获取前 {page_count} 页点赞数据，停止请求")
                elif not has_more:
                    logger.info("没有更多点赞数据")
                break
        except httpx.ReadTimeout:
            logger.error(f"请求点赞数据超时 (第 {page_count} 页)")
            break
        except Exception as error:
            logger.error(f"请求点赞数据失败 (第 {page_count} 页): {error}")
            break
    if duplicate_count > 0:
        logger.warning(f"点赞API总共返回了 {duplicate_count} 个重复ID，已过滤")
    logger.info(f"点赞数据总共获取到 {len(video_ids)} 个视频ID (去重后)，来自 {page_count} 页数据")
    
    # 如果第一页就没有获取到任何数据，说明Cookie可能已失效
    if page_count == 1 and len(video_ids) == 0:
        logger.error(f"[{user_label}] 第一页点赞数据为空，Cookie可能已失效，更新douyinlikesfailure标记")
        try:
            # 更新does表的douyinlikesfailure字段
            supabase.table("does").update({"douyinlikesfailure": True}).eq("id", 1).execute()
            logger.info(f"已将douyinlikesfailure标记设置为true，因为{user_label}的Cookie可能失效")
        except Exception as e:
            logger.error(f"更新douyinlikesfailure标记失败: {str(e)}")
    
    return video_ids

async def process_single_aweme(app, aweme_id, current_author_info=None, is_full_scan=False,
                               is_large_task_mode=False, is_liked_video=False,
                               liked_by_hulu=False, liked_by_500=False,
                               saved_by_hulu=False, saved_by_500=False, # Add saved parameters
                               is_retry_failed=False, is_window_task=False):
    if is_liked_video:
        log_prefix = "点赞视频"
        wdir_prefix = "liked"
    elif saved_by_hulu or saved_by_500: # Add condition for saved videos
        user_label = "hulu" if saved_by_hulu else "500"
        log_prefix = f"收藏视频({user_label})"
        wdir_prefix = "saved"
    elif is_large_task_mode:
        log_prefix = "大任务"
        wdir_prefix = "large"
    else:
        author_name_for_log = current_author_info.get('nickname') if current_author_info else aweme_id
        log_prefix = author_name_for_log
        wdir_prefix = "surveillance"

    logger.info(f"[{log_prefix}] 开始处理作品: {aweme_id}{' (重试失败任务)' if is_retry_failed else ''}")

    existing_file_id = None
    existing_description = None
    existing_failed = None
    existing_liked_by_hulu = False
    existing_liked_by_500 = False
    existing_saved_by_hulu = False # Read existing saved status
    existing_saved_by_500 = False # Read existing saved status
    try:
        # Select all relevant boolean flags
        resp = supabase.table("douyin").select("file_id, description, failed, liked_by_hulu, liked_by_500, saved_by_hulu, saved_by_500") \
                          .eq("aweme_id", aweme_id).limit(1).execute()
        existing_data = resp.data[0] if resp.data else None
        if existing_data:
            existing_file_id = existing_data.get("file_id")
            existing_description = existing_data.get("description")
            existing_failed = existing_data.get("failed")
            existing_liked_by_hulu = existing_data.get("liked_by_hulu", False)
            existing_liked_by_500 = existing_data.get("liked_by_500", False)
            existing_saved_by_hulu = existing_data.get("saved_by_hulu", False) # Assign existing saved status
            existing_saved_by_500 = existing_data.get("saved_by_500", False) # Assign existing saved status
            
            # 窗口任务特殊处理：如果作品不存在或已失败，跳过处理
            if is_window_task:
                if not existing_file_id:
                    logger.info(f"[窗口任务] 作品 {aweme_id} 在数据库中没有 file_id，跳过处理")
                    return False
                if existing_failed:
                    logger.info(f"[窗口任务] 作品 {aweme_id} 已标记为失败: {existing_failed}，跳过处理")
                    return False
            
            # If processing a liked/saved video, ensure the corresponding flag is set
            # This prevents accidentally unsetting the flag if the video was processed before for another reason
            liked_by_hulu = liked_by_hulu or existing_liked_by_hulu
            liked_by_500 = liked_by_500 or existing_liked_by_500
            saved_by_hulu = saved_by_hulu or existing_saved_by_hulu
            saved_by_500 = saved_by_500 or existing_saved_by_500

            # If work is marked as unavailable, skip processing but update liked/saved flags
            if existing_failed == "not available":
                logger.info(f"作品 {aweme_id} 已标记为不可用(not available)，跳过处理，仅更新liked/saved标记")
                update_data = {"aweme_id": aweme_id}
                if liked_by_hulu and not existing_liked_by_hulu:
                    update_data["liked_by_hulu"] = True
                if liked_by_500 and not existing_liked_by_500:
                    update_data["liked_by_500"] = True
                if saved_by_hulu and not existing_saved_by_hulu: # Check and update saved flags
                    update_data["saved_by_hulu"] = True
                if saved_by_500 and not existing_saved_by_500: # Check and update saved flags
                    update_data["saved_by_500"] = True
                if len(update_data) > 1: # Check if there's anything to update besides aweme_id
                    try:
                        merged = sanitize_and_merge(existing_data, update_data)
                        supabase.table("douyin").upsert(merged).execute()
                        logger.info(f"已更新不可用作品 {aweme_id} 的 liked/saved 标记: {update_data}")
                    except Exception as ue:
                        logger.error(f"更新不可用作品 {aweme_id} liked/saved 标记时出错: {ue}")
                return True # Return success as we handled the flags
                
            # Determine if we should skip based on context (liked, saved, normal surveillance)
            should_skip = False
            if (is_liked_video or saved_by_hulu or saved_by_500) and existing_file_id and existing_description:
                logger.info(f"作品 {aweme_id} (liked/saved) 已有完整数据，跳过重新处理，仅更新liked/saved标记")
                should_skip = True
            elif not is_retry_failed and not is_large_task_mode and not is_liked_video and not saved_by_hulu and not saved_by_500 and existing_file_id and existing_description:
                logger.info(f"作品 {aweme_id} (非重试/large/liked/saved) 且已有数据，跳过处理")
                should_skip = True
            
            if should_skip:
                update_data = {"aweme_id": aweme_id} # Ensure aweme_id is always present
                if liked_by_hulu and not existing_liked_by_hulu:
                    update_data["liked_by_hulu"] = True
                if liked_by_500 and not existing_liked_by_500:
                    update_data["liked_by_500"] = True
                if saved_by_hulu and not existing_saved_by_hulu: # Update saved flags if needed
                    update_data["saved_by_hulu"] = True
                if saved_by_500 and not existing_saved_by_500: # Update saved flags if needed
                    update_data["saved_by_500"] = True
                
                if len(update_data) > 1: # Only update if flags changed
                    try:
                        merged = sanitize_and_merge(existing_data, update_data)
                        supabase.table("douyin").upsert(merged).execute()
                        logger.info(f"已更新跳过作品 {aweme_id} 的 liked/saved 标记: {update_data}")
                    except Exception as ue:
                        logger.error(f"更新跳过作品 {aweme_id} liked/saved 标记时出错: {ue}")
                return True # Return success
    except Exception as e:
        logger.debug(f"检查作品 {aweme_id} 是否存在时出错: {str(e)}")

    wdir = os.path.join("downloads", f"{wdir_prefix}_{aweme_id}")
    os.makedirs(wdir, exist_ok=True)
    success = False
    current_sec_uid = current_author_info.get('sec_uid') if current_author_info else None
    current_uid = current_author_info.get('uid') if current_author_info else None

    try:
        resp_data = await fetch_one_video_api(aweme_id)
        if resp_data.get('data', {}).get('unavailable'):
            error_msg = resp_data.get('data', {}).get('aweme_detail', {}).get('desc', '作品不可用')
            logger.warning(f"作品 {aweme_id} 不可用: {error_msg}")
            # 窗口任务遇到不可用作品时，保护现有数据，不进行任何更新
            if is_window_task:
                logger.info(f"窗口任务: 作品 {aweme_id} 已不可用，跳过处理以保护现有数据")
                return False  # 返回False表示处理失败
            minimal_data = {
                'base_info': {'aweme_id': aweme_id, 'desc': error_msg},
                'author_info': {'uid': current_uid, 'sec_uid': current_sec_uid}
            }
            await save_douyin_to_database(minimal_data, is_full_scan, "not available",
                                          liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                          liked_by_500=liked_by_500 or existing_liked_by_500,
                                          saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                          saved_by_500=saved_by_500 or existing_saved_by_500)
            logger.info(f"已将作品 {aweme_id} 标记为不可用(not available)")
            return True
        
        # 检查是否有filter_detail，这表示作品存在但不可用（被删除或权限问题）
        filter_detail = resp_data.get('data', {}).get('filter_detail', {})
        if filter_detail and not resp_data.get('data', {}).get('aweme_detail'):
            detail_msg = filter_detail.get('detail_msg', '作品可能已被删除或设为私密')
            aweme_id_from_filter = filter_detail.get('aweme_id', aweme_id)
            logger.warning(f"作品 {aweme_id_from_filter} 存在但不可用: {detail_msg}")
            # 窗口任务遇到不可用作品时，保护现有数据，不进行任何更新
            if is_window_task:
                logger.info(f"窗口任务: 作品 {aweme_id_from_filter} 已不可用，跳过处理以保护现有数据")
                return False  # 返回False表示处理失败
            minimal_data = {
                'base_info': {'aweme_id': aweme_id_from_filter, 'desc': detail_msg},
                'author_info': {'uid': current_uid, 'sec_uid': current_sec_uid}
            }
            await save_douyin_to_database(minimal_data, is_full_scan, "not available",
                                          liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                          liked_by_500=liked_by_500 or existing_liked_by_500,
                                          saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                          saved_by_500=saved_by_500 or existing_saved_by_500)
            logger.info(f"已将作品 {aweme_id_from_filter} 标记为不可用(not available)，跳过下载和处理")
            return True
        
        parsed_data = parse_douyin_work(resp_data)
        if not parsed_data or not parsed_data.get('base_info', {}).get('aweme_id'):
            error_msg = f"作品 {aweme_id} 解析失败"
            logger.error(error_msg)
            # 窗口任务解析失败时，不更新数据库，保护现有数据
            if is_window_task:
                logger.info(f"窗口任务: 作品 {aweme_id} 解析失败，跳过处理以保护现有数据")
                return False
            minimal_data = {
                'base_info': {'aweme_id': aweme_id},
                'author_info': {'uid': current_uid, 'sec_uid': current_sec_uid}
            }
            await save_douyin_to_database(minimal_data, is_full_scan, error_msg,
                                          liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                          liked_by_500=liked_by_500 or existing_liked_by_500,
                                          saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                          saved_by_500=saved_by_500 or existing_saved_by_500)
            return False

        # 窗口任务不需要保存数据，只需要获取新的file_id并更新high_quality字段
        if not is_window_task:
            save_result = await save_douyin_to_database(
                parsed_data,
                is_full_scan=is_full_scan,
                failed_reason=None,
                liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                liked_by_500=liked_by_500 or existing_liked_by_500,
                saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                saved_by_500=saved_by_500 or existing_saved_by_500
            )
            if save_result.get("status") == "error":
                error_msg = f"保存作品 {aweme_id} 数据到数据库失败: {save_result.get('message')}"
                logger.error(error_msg)
                await save_douyin_to_database(parsed_data, is_full_scan, error_msg,
                                              liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                              liked_by_500=liked_by_500 or existing_liked_by_500,
                                              saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                              saved_by_500=saved_by_500 or existing_saved_by_500)
                return False
        else:
            logger.info(f"窗口任务: 跳过作品 {aweme_id} 的数据保存，仅获取新的file_id")

        try:
            caption_text = build_caption_for_single(parsed_data)
            media_info = parsed_data.get('media_info', {})
            images = media_info.get('images', [])
            play_url = media_info.get('play_url', "")
            backup_urls = media_info.get('backup_urls', [])
            music_url = parsed_data.get('music_info', {}).get('play_url', "")

            download_tasks = []
            if images:
                for idx, img_obj in enumerate(images):
                    img_url = img_obj.get('url')
                    if img_url:
                        base_name = f"{aweme_id}_{idx}"
                        download_tasks.append({
                            'base_name': base_name,
                            'url': img_url,
                            'task': download_file(img_url),
                            'type': 'image',
                            'default_ext': '.jpg'
                        })
                    vid = img_obj.get('video')
                    if vid and vid.get('url'):
                        base_name = f"{aweme_id}_{idx}_video"
                        vid_backup_urls = vid.get('backup_urls', [])
                        if is_retry_failed or is_large_task_mode:
                            logger.info(f"为视频设置 {len(vid_backup_urls)} 个备用URL")
                            task = download_file(vid.get('url'), backup_urls=vid_backup_urls)
                        else:
                            task = download_file(vid.get('url'))
                        download_tasks.append({
                            'base_name': base_name,
                            'url': vid.get('url'),
                            'task': task,
                            'type': 'video',
                            'default_ext': '.mp4'
                        })
            elif play_url:
                base_name = f"{aweme_id}"
                if is_retry_failed or is_large_task_mode:
                    logger.info(f"为视频设置 {len(backup_urls)} 个备用URL")
                    task = download_file(play_url, backup_urls=backup_urls)
                else:
                    task = download_file(play_url)
                download_tasks.append({
                    'base_name': base_name,
                    'url': play_url,
                    'task': task,
                    'type': 'video',
                    'default_ext': '.mp4'
                })

            if music_url:
                base_name = f"{aweme_id}_music"
                download_tasks.append({
                    'base_name': base_name,
                    'url': music_url,
                    'task': download_file(music_url),
                    'type': 'audio',
                    'default_ext': '.mp3'
                })

            downloaded_files = []
            any_download_failed = False
            if download_tasks:
                logger.info(f"开始并行下载作品 {aweme_id} 的 {len(download_tasks)} 个媒体文件")
                
                # 先修改为新的格式处理
                new_tasks = []
                for task in download_tasks:
                    if isinstance(task, tuple):
                        # 旧格式：(path, download_task)
                        # 需要从 path 推断基础信息
                        path = task[0]
                        base_name = os.path.splitext(os.path.basename(path))[0]
                        old_ext = os.path.splitext(path)[1]
                        file_type = 'video' if old_ext == '.mp4' else ('audio' if old_ext == '.mp3' else 'image')
                        new_tasks.append({
                            'base_name': base_name,
                            'url': '',  # 无法从旧格式获取 URL
                            'task': task[1],
                            'type': file_type,
                            'default_ext': old_ext
                        })
                    else:
                        new_tasks.append(task)
                
                # 执行下载任务
                tasks = [t['task'] for t in new_tasks]
                download_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, (task_info, result) in enumerate(zip(new_tasks, download_results)):
                    if isinstance(result, Exception):
                        logger.error(f"下载文件失败: {str(result)}")
                        any_download_failed = True
                        continue
                    if not result or not result.get("binary_data"):
                        logger.error(f"下载文件失败: 返回数据为空")
                        any_download_failed = True
                        continue
                    
                    try:
                        # 根据 URL 和 content-type 确定文件扩展名
                        actual_url = result.get("url", task_info['url'])
                        content_type = result.get("content_type", "")
                        ext = determine_file_extension(actual_url, content_type, task_info['default_ext'])
                        
                        # 构建最终文件路径
                        file_path = os.path.join(wdir, f"{task_info['base_name']}{ext}")
                        
                        # 保存文件
                        with open(file_path, "wb") as f:
                            f.write(result.get("binary_data"))
                        downloaded_files.append(file_path)
                        logger.info(f"成功下载并保存文件: {file_path} (类型: {task_info['type']}, 扩展名: {ext})")
                    except Exception as save_err:
                        logger.error(f"保存文件失败: {str(save_err)}")
                        any_download_failed = True
                if any_download_failed:
                    logger.warning(f"作品 {aweme_id} 的部分媒体文件下载失败，成功: {len(downloaded_files)}/{len(download_tasks)}")
                else:
                    logger.info(f"完成作品 {aweme_id} 的媒体下载，成功: {len(downloaded_files)}/{len(download_tasks)}")

            if downloaded_files:
                aweme_author_sec_uid = parsed_data.get('author_info', {}).get('sec_uid')
                send_sec_uid = aweme_author_sec_uid or current_sec_uid
                # 判断是否有部分失败
                failed_reason_to_pass = None
                if any_download_failed:
                    failed_reason_to_pass = "作品下载不完整，部分文件失败"
                
                result_string = await send_media_files(
                    app, TELEGRAM_CHAT_ID, downloaded_files,
                    f"https://www.douyin.com/video/{aweme_id}", caption_text,
                    send_sec_uid, "", True, {"send_file": True}, aweme_id,
                    liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                    liked_by_500=liked_by_500 or existing_liked_by_500,
                    saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                    saved_by_500=saved_by_500 or existing_saved_by_500,
                    is_window_task=is_window_task,
                    is_large_task_mode=is_large_task_mode,
                    is_liked_video=is_liked_video,
                    failed_reason=failed_reason_to_pass  # 传递失败原因
                )
                if (result_string and not result_string.startswith(";")) or "UPDATED" in result_string or (is_liked_video or saved_by_hulu or saved_by_500) or "WINDOW_TASK" in result_string:
                    success = True
                    # 窗口任务时，保存返回的file_id供后续使用
                    if is_window_task and result_string:
                        return result_string  # 返回file_id字符串
                    
                    # file_id和failed字段已经在send_media_files中一次性更新了
                    if any_download_failed:
                        logger.warning(f"作品 {aweme_id} 部分文件下载失败，但已成功发送可用文件")
                    else:
                        logger.info(f"作品 {aweme_id} 成功处理完成")
                else:
                    error_msg = f"作品 {aweme_id} 媒体发送/数据库更新未完全成功: {result_string}"
                    logger.error(error_msg)
                    await save_douyin_to_database(parsed_data, is_full_scan, error_msg,
                                                  liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                                  liked_by_500=liked_by_500 or existing_liked_by_500,
                                                  saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                                  saved_by_500=saved_by_500 or existing_saved_by_500)
                    success = False
            else:
                error_msg = f"作品 {aweme_id} 没有成功下载的媒体文件"
                logger.error(error_msg)
                await save_douyin_to_database(parsed_data, is_full_scan, error_msg,
                                              liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                              liked_by_500=liked_by_500 or existing_liked_by_500,
                                              saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                              saved_by_500=saved_by_500 or existing_saved_by_500)
                success = False
        except Exception as e:
            error_msg = f"处理作品 {aweme_id} 下载/发送时出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await save_douyin_to_database(parsed_data or {},
                                          is_full_scan, error_msg,
                                          liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                          liked_by_500=liked_by_500 or existing_liked_by_500,
                                          saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                          saved_by_500=saved_by_500 or existing_saved_by_500)
            success = False
            is_rate_limit = await handle_rate_limit_error(e, aweme_id)
            if is_rate_limit:
                logger.warning(f"作品 {aweme_id} 遭遇限流，将由上层决定是否重试。")
                try:
                    rate_limit_reason = "Rate Limit Detected"
                    await save_douyin_to_database(parsed_data or {},
                                                  is_full_scan, rate_limit_reason,
                                                  liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                                  liked_by_500=liked_by_500 or existing_liked_by_500,
                                                  saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                                  saved_by_500=saved_by_500 or existing_saved_by_500)
                except Exception as db_save_err:
                    logger.error(f"记录作品 {aweme_id} 限流失败原因时出错: {db_save_err}")
                return False
    except ConnectionError:
        logger.warning(f"作品 {aweme_id} 处理时遭遇连接错误（可能为限流），将由上层决定是否重试。")
        try:
            connection_error_reason = "Connection Error (Rate Limit)"
            minimal_data_conn_err = {
                'base_info': {'aweme_id': aweme_id},
                'author_info': {'uid': current_uid, 'sec_uid': current_sec_uid}
            }
            await save_douyin_to_database(minimal_data_conn_err,
                                          is_full_scan, connection_error_reason,
                                          liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                          liked_by_500=liked_by_500 or existing_liked_by_500,
                                          saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                          saved_by_500=saved_by_500 or existing_saved_by_500)
        except Exception as db_save_err:
            logger.error(f"记录作品 {aweme_id} 连接错误失败原因时出错: {db_save_err}")
        return False
    except Exception as e:
        error_msg = f"获取或解析作品 {aweme_id} 时出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        minimal_data = {
            'base_info': {'aweme_id': aweme_id},
            'author_info': {'uid': current_uid, 'sec_uid': current_sec_uid}
        }
        await save_douyin_to_database(minimal_data,
                                      is_full_scan, error_msg,
                                      liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                      liked_by_500=liked_by_500 or existing_liked_by_500,
                                      saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                      saved_by_500=saved_by_500 or existing_saved_by_500)
        success = False
        is_rate_limit = await handle_rate_limit_error(e, aweme_id)
        if is_rate_limit:
            logger.warning(f"作品 {aweme_id} 遭遇限流，将由上层决定是否重试。")
            try:
                rate_limit_reason = "Rate Limit Detected"
                await save_douyin_to_database(parsed_data or minimal_data,
                                              is_full_scan, rate_limit_reason,
                                              liked_by_hulu=liked_by_hulu or existing_liked_by_hulu,
                                              liked_by_500=liked_by_500 or existing_liked_by_500,
                                              saved_by_hulu=saved_by_hulu or existing_saved_by_hulu,
                                              saved_by_500=saved_by_500 or existing_saved_by_500)
            except Exception as db_save_err:
                logger.error(f"记录作品 {aweme_id} 限流失败原因时出错: {db_save_err}")
            return False
    finally:
        if DELETE_FILES and os.path.exists(wdir):
            try:
                shutil.rmtree(wdir)
            except Exception as e:
                logger.error(f"删除目录 {wdir} 失败: {str(e)}")

    return success

async def process_author_works(app, author_info, is_full_scan=False, should_fetch_profile=True):
    sec_user_id = author_info.get('sec_uid')
    author_name = author_info.get('nickname') or author_info.get('unique_id') or sec_user_id
    logger.info(f"开始处理作者: {author_name} (sec_uid={sec_user_id})")
    
    # 检查是否只需要获取作者信息而不处理作品
    once_enabled = author_info.get('once') is True
    
    # 如果once不为True，则只处理作者信息
    if not once_enabled:
        logger.info(f"作者 {author_name} 的 once!=True, 只处理作者信息不获取作品")
        if should_fetch_profile:
            try:
                # 获取作者资料
                profile_json = await fetch_user_profile_api(sec_user_id)
                
                # 检查API是否获取成功
                if not profile_json or not profile_json.get("data"):
                    logger.error(f"作者 {author_name}: 获取用户资料API失败，跳过处理")
                    return {"ok": False, "processed": 0, "failed": 0, "error": "获取用户资料API失败", "info_only": True}
                
                user_json = profile_json.get("data", {}).get("user", {})
                live_payload = build_live_payload(user_json)
                upsert_live_info_to_db(user_json, live_payload)
                logger.info(f"已更新作者资料: {author_name}")
                
                # 更新最后获取时间
                await update_author_last_fetched_at(sec_user_id)
                return {"ok": True, "processed": 0, "failed": 0, "info_only": True}
            except Exception as e:
                logger.error(f"处理作者 {author_name} 信息时出错: {str(e)}", exc_info=True)
                return {"ok": False, "processed": 0, "failed": 0, "error": str(e), "info_only": True}
        else:
            logger.info(f"should_fetch_profile=False，跳过作者 {author_name} 的资料获取")
            # 只更新最后获取时间
            await update_author_last_fetched_at(sec_user_id)
            return {"ok": True, "processed": 0, "failed": 0, "info_only": True, "skipped": True}
    
    aweme_ids = []
    max_cursor = 0
    has_more = True
    page_count = 0
    use_tikhub_api = False

    try:
        if should_fetch_profile:
            # 并行请求作者资料和第一页作品列表
            logger.info(f"并行获取作者资料和作品列表: {author_name}")
            profile_task = fetch_user_profile_api(sec_user_id)
            posts_task = fetch_user_post_videos_api(sec_user_id, max_cursor, 40)
            
            # 同时等待两个任务完成
            profile_json, resp_json = await asyncio.gather(profile_task, posts_task)
            
            # 检查API是否获取成功
            if not profile_json or not profile_json.get("data"):
                logger.error(f"作者 {author_name}: 获取用户资料API失败，跳过处理")
                return {"ok": False, "processed": 0, "failed": 0, "error": "获取用户资料API失败"}
            
            if not resp_json or not resp_json.get("data"):
                logger.error(f"作者 {author_name}: 获取作品列表API失败，跳过处理")
                return {"ok": False, "processed": 0, "failed": 0, "error": "获取作品列表API失败"}
            
            # 处理作者资料
            try:
                user_json = profile_json.get("data", {}).get("user", {})
                live_payload = build_live_payload(user_json)
                upsert_live_info_to_db(user_json, live_payload)
                logger.info(f"已更新作者资料: {author_name}")
            except Exception as live_err:
                logger.error(f"获取/更新作者 {author_name} 直播信息失败: {live_err}")
        else:
            # 只获取作品列表，不获取作者资料
            resp_json = await fetch_user_post_videos_api(sec_user_id, max_cursor, 40)
            
            # 检查API是否获取成功
            if not resp_json or not resp_json.get("data"):
                logger.error(f"作者 {author_name}: 获取作品列表API失败，跳过处理")
                return {"ok": False, "processed": 0, "failed": 0, "error": "获取作品列表API失败"}

        logger.info(f"开始{('完整' if is_full_scan else '增量')}处理作者 {author_name} ({sec_user_id}) 的作品")
        
        # 检查API响应是否有效
        if resp_json is None:
            logger.error(f"作者 {author_name}: 获取作品列表API返回None，请检查API连接")
            return {"ok": False, "processed": 0, "failed": 0, "error": "API返回None"}
            
        # 记录API响应的完整结构
        logger.debug(f"作者 {author_name}: API响应结构: {json.dumps(resp_json, ensure_ascii=False)[:500]}...")

        # 安全获取data字段
        data = resp_json.get('data', {})
        if data is None:
            logger.error(f"作者 {author_name}: API响应中'data'字段为None")
            data = {}
            
        # 安全获取aweme_list字段
        aweme_list_raw = data.get('aweme_list')
        if aweme_list_raw is None:
            logger.warning(f"作者 {author_name}: API响应中'aweme_list'字段为None，将使用空列表")
            aweme_list = []
        elif not isinstance(aweme_list_raw, list):
            logger.warning(f"作者 {author_name}: API响应中'aweme_list'字段不是列表类型 (实际类型: {type(aweme_list_raw).__name__})，将转换为空列表")
            aweme_list = []
        else:
            aweme_list = aweme_list_raw

        while has_more:
            if page_count > 0:  # 第一页已在并行请求中获取，这里处理后续页
                logger.info(f"获取作者 {author_name} 的第 {page_count+1} 页作品")
                if not use_tikhub_api:
                    resp_json = await fetch_user_post_videos_api(sec_user_id, max_cursor, 40)
                else:
                    resp_json = await fetch_user_post_videos_from_tikhub(sec_user_id, max_cursor, 40)
                
                # 检查API响应是否有效
                if resp_json is None:
                    logger.error(f"作者 {author_name}: 获取第 {page_count+1} 页作品列表API返回None")
                    break
                    
                # 安全获取data字段
                data = resp_json.get('data', {})
                if data is None:
                    logger.error(f"作者 {author_name}: 第 {page_count+1} 页API响应中'data'字段为None")
                    break
                    
                # 安全获取aweme_list字段
                aweme_list_raw = data.get('aweme_list')
                if aweme_list_raw is None:
                    logger.warning(f"作者 {author_name}: 第 {page_count+1} 页API响应中'aweme_list'字段为None，将使用空列表")
                    aweme_list = []
                elif not isinstance(aweme_list_raw, list):
                    logger.warning(f"作者 {author_name}: 第 {page_count+1} 页API响应中'aweme_list'字段不是列表类型，将转换为空列表")
                    aweme_list = []
                else:
                    aweme_list = aweme_list_raw

            page_count += 1

            if page_count > 1 and not aweme_list and has_more and not use_tikhub_api:
                logger.warning(f"检测到原API第{page_count}页返回空数据，切换到TikHub API")
                use_tikhub_api = True
                max_cursor = 0
                page_count = 0
                aweme_ids = []
                logger.info(f"检测到原API数据获取异常，切换到备用API重新获取作者 {author_name} 的所有作品")
                continue

            original_count = len(aweme_list)
            filtered_aweme_list = []
            for item in aweme_list:
                if isinstance(item, dict):
                    item_author = item.get('author', {})
                    item_sec_uid = item_author.get('sec_uid')
                    item_aweme_id = item.get('aweme_id')
                    if item_sec_uid == sec_user_id and item_aweme_id:
                        filtered_aweme_list.append(item)
                    elif item_aweme_id:
                        logger.debug(f"作品 {item_aweme_id} 的作者 sec_uid ({item_sec_uid}) 与当前作者 ({sec_user_id}) 不符，已过滤")
            filtered_count = len(filtered_aweme_list)
            if original_count != filtered_count:
                logger.info(f"作者 {author_name} 第 {page_count} 页: 原始获取 {original_count} 个, 过滤后剩余 {filtered_count} 个 (sec_uid 匹配)")

            for item in filtered_aweme_list:
                aweme_ids.append(item.get('aweme_id'))

            if not is_full_scan:
                break

            # 安全获取has_more字段
            has_more_raw = data.get('has_more')
            has_more = bool(has_more_raw)  # 转换为布尔值
            
            # 安全获取max_cursor字段
            max_cursor_raw = data.get('max_cursor')
            max_cursor = 0 if max_cursor_raw is None else max_cursor_raw
            
            logger.info(f"作者 {author_name}: 第{page_count}页有效作品 {filtered_count} 个, 累计 {len(aweme_ids)}, has_more={has_more}, API={('TikHub' if use_tikhub_api else '原始')}")
            await asyncio.sleep(2)

        if not aweme_ids:
            logger.info(f"未找到作者 {author_name} 的任何自有作品")
            return {"ok": True, "processed": 0, "failed": 0}

        logger.info(f"找到作者 {author_name} 的 {len(aweme_ids)} 个自有作品，开始处理...")

        ids_to_process = aweme_ids
        if not is_full_scan:
            try:
                batch_size = 100
                existing_ids = set()
                for i in range(0, len(aweme_ids), batch_size):
                    batch_ids = aweme_ids[i:i+batch_size]
                    if not batch_ids:
                        continue
                    logger.info(f"查询作者 {author_name} 第 {i//batch_size + 1} 批作品记录 (共 {len(batch_ids)} 个ID)")
                    response = supabase.table("douyin") \
                                      .select("aweme_id") \
                                      .eq("sec_uid", sec_user_id) \
                                      .in_("aweme_id", batch_ids) \
                                      .execute()
                    batch_existing = set(item.get('aweme_id') for item in (response.data or []))
                    existing_ids.update(batch_existing)
                    logger.debug(f"第 {i//batch_size + 1} 批查询发现 {len(batch_existing)} 个已存在作品")
                logger.info(f"作者 {author_name} 在数据库中已有 {len(existing_ids)} 个作品记录")
                new_ids = [id for id in aweme_ids if id not in existing_ids]
                if not new_ids:
                    logger.info(f"作者 {author_name} 没有新的自有作品需要处理")
                    return {"ok": True, "processed": 0, "failed": 0}
                logger.info(f"作者 {author_name} 有 {len(new_ids)} 个新的自有作品需要处理")
                ids_to_process = new_ids
            except Exception as e:
                logger.error(f"获取作者 {author_name} 已有作品失败: {str(e)}")
                ids_to_process = aweme_ids

        processed = 0
        failed = 0
        total_to_process = len(ids_to_process)

        for i, aweme_id in enumerate(ids_to_process):
            logger.info(f"作者 {author_name}: 处理第 {i+1}/{total_to_process} 个自有作品: {aweme_id}")
            try:
                selected_bot = get_bot_for_task()
                result_ok = await process_single_aweme(
                    selected_bot, aweme_id, current_author_info=author_info, is_full_scan=is_full_scan
                )
                if result_ok:
                    processed += 1
                else:
                    failed += 1
            except ConnectionError:
                logger.warning(f"遭遇限流，暂停处理作者 {author_name} 剩余作品 60 秒...")
                failed += 1
                await asyncio.sleep(60)
                continue
            except Exception as e:
                logger.error(f"处理作品 {aweme_id} 时发生意外错误: {str(e)}", exc_info=True)
                failed += 1
            await asyncio.sleep(PROCESS_DELAY)

        logger.info(f"完成处理作者 {author_name} 的自有作品 (增量更新)，总计尝试: {total_to_process}, 成功/跳过: {processed}, 失败: {failed}")
    except Exception as e:
        logger.error(f"处理作者 {author_name} 的作品列表时出错: {str(e)}", exc_info=True)
        return {"ok": False, "processed": 0, "failed": 0, "error": str(e)}

    # 确保在try块成功结束时也有返回值
    return {"ok": True, "processed": processed, "failed": failed}

async def get_surveilled_authors(page=0, page_size=1000):
    """
    分页获取需要监控的作者
    
    Args:
        page: 页码，从0开始
        page_size: 每页大小，默认1000（Supabase最大限制）
    
    Returns:
        作者列表
    """
    try:
        offset = page * page_size
        response = supabase.table("douyin_user").select("*") \
                           .or_("surveillance.eq.True,record.eq.True") \
                           .order("last_fetched_at") \
                           .range(offset, offset + page_size - 1) \
                           .execute()
        authors = response.data or []
        logger.info(f"分页获取监控作者: 第{page}页, 大小{page_size}, 获取到{len(authors)}个作者")
        return authors
    except Exception as e:
        logger.error(f"分页查询监控作者异常: {str(e)}")
        return []

async def update_author_last_fetched_at(sec_user_id):
    try:
        existing_res = supabase.table("douyin_user").select("*").eq("sec_uid", sec_user_id).limit(1).execute()
        existing_row = existing_res.data[0] if existing_res.data else {}
        new_data = {"sec_uid": sec_user_id, "last_fetched_at": datetime.now().isoformat()}
        final_data = sanitize_and_merge(existing_row, new_data)
        supabase.table("douyin_user").upsert(final_data).execute()
    except Exception as e:
        logger.error(f"更新作者最后获取时间异常: {str(e)}")

async def process_liked_videos(app, cookie: str, user_label: str, db_field_liked: str, is_full_scan=False):
    scan_type = "全量" if is_full_scan else "增量(第一页)"
    logger.info(f"===== 开始处理 [{user_label}] 的点赞视频 ({scan_type}) =====")
    aweme_ids = await fetch_liked_videos_api(cookie, user_label, is_full_scan)
    if not aweme_ids:
        logger.info(f"[{user_label}] 没有获取到需要处理的点赞视频ID")
        return True

    total_liked = len(aweme_ids)
    logger.info(f"从API获取到 [{user_label}] 的 {total_liked} 个点赞视频ID")
    try:
        batch_size = 100
        existing_liked_ids = set()
        for i in range(0, len(aweme_ids), batch_size):
            batch_ids = aweme_ids[i:i+batch_size]
            logger.info(f"[{user_label}] 查询第 {i//batch_size + 1} 批点赞视频记录 (共 {len(batch_ids)} 个ID)")
            if not batch_ids:
                continue
            response = supabase.table("douyin") \
                              .select("aweme_id") \
                              .eq(db_field_liked, True) \
                              .in_("aweme_id", batch_ids) \
                              .execute()
            batch_existing = set(item.get('aweme_id') for item in (response.data or []))
            existing_liked_ids.update(batch_existing)
            logger.debug(f"[{user_label}] 第 {i//batch_size + 1} 批查询发现 {len(batch_existing)} 个已存在点赞视频")

        logger.info(f"[{user_label}] 数据库中已存在 {len(existing_liked_ids)} 个点赞视频记录")
        new_liked_ids = [id for id in aweme_ids if id not in existing_liked_ids]
        if not new_liked_ids:
            logger.info(f"[{user_label}] 没有新的点赞视频需要处理")
            return True
        logger.info(f"筛选出 [{user_label}] 的 {len(new_liked_ids)} 个新的点赞视频需要处理")
        aweme_ids = new_liked_ids
    except Exception as e:
        logger.error(f"筛选 [{user_label}] 点赞视频时出错: {str(e)}")
        logger.warning(f"由于筛选出错，将处理所有获取到的 [{user_label}] 点赞视频")

    processed_liked = 0
    failed_liked = 0
    total_to_process = len(aweme_ids)
    logger.info(f"开始处理 [{user_label}] 的 {total_to_process} 个新的点赞视频...")

    for i, aweme_id in enumerate(aweme_ids):
        logger.info(f"[{user_label} 点赞处理 {i+1}/{total_to_process}] 处理作品: {aweme_id}")
        try:
            liked_kwargs = {}
            if db_field_liked == "liked_by_hulu":
                liked_kwargs = {"liked_by_hulu": True}
            elif db_field_liked == "liked_by_500":
                liked_kwargs = {"liked_by_500": True}
            result_ok = await process_single_aweme(
                app,
                aweme_id,
                is_full_scan=is_full_scan,
                is_liked_video=True,
                **liked_kwargs
            )
            if result_ok:
                processed_liked += 1
            else:
                failed_liked += 1
        except ConnectionError:
            logger.warning(f"[{user_label}] 遭遇限流，暂停处理点赞视频 60 秒...")
            failed_liked += 1
            await asyncio.sleep(60)
            continue
        except Exception as e:
            logger.error(f"处理 [{user_label}] 点赞视频 {aweme_id} 时发生意外错误: {str(e)}", exc_info=True)
            failed_liked += 1
        await asyncio.sleep(max(1, PROCESS_DELAY))

    logger.info(f"===== [{user_label}] 点赞视频处理完成 ({scan_type}) =====")
    logger.info(f"[{user_label}] 总计尝试: {total_to_process}, 成功/跳过: {processed_liked}, 失败: {failed_liked}")
    return failed_liked == 0

async def process_collected_videos(app, cookie: str, user_label: str, db_field_saved: str):
    """
    处理指定用户的全部收藏视频。
    Args:
        app: Pyrogram Client 实例
        cookie: 用户 Cookie
        user_label: 用户标识 (如 "hulu", "500")
        db_field_saved: 数据库中对应的收藏标记字段名 (如 "saved_by_hulu")
    """
    logger.info(f"===== 开始处理 [{user_label}] 的收藏视频 (全量) =====")
    aweme_ids = await fetch_collected_videos_api(cookie, user_label)
    if not aweme_ids:
        logger.info(f"[{user_label}] 没有获取到需要处理的收藏视频ID")
        return True # Considered success if no videos to process

    total_collected = len(aweme_ids)
    logger.info(f"从API获取到 [{user_label}] 的 {total_collected} 个收藏视频ID")

    # --- 筛选出数据库中尚未标记为该用户收藏的视频 ---
    try:
        batch_size = 100
        existing_saved_ids = set()
        for i in range(0, len(aweme_ids), batch_size):
            batch_ids = aweme_ids[i:i+batch_size]
            logger.info(f"[{user_label}] 查询第 {i//batch_size + 1} 批收藏视频记录 (共 {len(batch_ids)} 个ID) 是否已标记 {db_field_saved}=True")
            if not batch_ids:
                continue
            response = supabase.table("douyin") \
                              .select("aweme_id") \
                              .eq(db_field_saved, True) \
                              .in_("aweme_id", batch_ids) \
                              .execute()
            batch_existing = set(item.get('aweme_id') for item in (response.data or []))
            existing_saved_ids.update(batch_existing)
            logger.debug(f"[{user_label}] 第 {i//batch_size + 1} 批查询发现 {len(batch_existing)} 个已标记为收藏")

        logger.info(f"[{user_label}] 数据库中已标记为 {db_field_saved}=True 的视频有 {len(existing_saved_ids)} 个")
        new_saved_ids = [id for id in aweme_ids if id not in existing_saved_ids]

        if not new_saved_ids:
            logger.info(f"[{user_label}] 没有新的收藏视频需要处理")
            return True
        logger.info(f"筛选出 [{user_label}] 的 {len(new_saved_ids)} 个新的收藏视频需要处理")
        aweme_ids_to_process = new_saved_ids
    except Exception as e:
        logger.error(f"筛选 [{user_label}] 收藏视频时出错: {str(e)}")
        logger.warning(f"由于筛选出错，将尝试处理所有获取到的 [{user_label}] 收藏视频")
        aweme_ids_to_process = aweme_ids

    # --- 处理新的收藏视频 ---
    processed_saved = 0
    failed_saved = 0
    total_to_process = len(aweme_ids_to_process)
    logger.info(f"开始处理 [{user_label}] 的 {total_to_process} 个新的收藏视频...")

    for i, aweme_id in enumerate(aweme_ids_to_process):
        logger.info(f"[{user_label} 收藏处理 {i+1}/{total_to_process}] 处理作品: {aweme_id}")
        try:
            saved_kwargs = {}
            if db_field_saved == "saved_by_hulu":
                saved_kwargs = {"saved_by_hulu": True}
            elif db_field_saved == "saved_by_500":
                saved_kwargs = {"saved_by_500": True}

            # Call process_single_aweme with saved flags
            result_ok = await process_single_aweme(
                app,
                aweme_id,
                is_full_scan=True, # Always treat saved video processing as needing full data if missing
                is_liked_video=False, # Mark as not a liked video explicitly
                **saved_kwargs
            )
            if result_ok:
                processed_saved += 1
            else:
                failed_saved += 1
        except ConnectionError:
            logger.warning(f"[{user_label}] 遭遇限流，暂停处理收藏视频 60 秒...")
            failed_saved += 1
            await asyncio.sleep(60)
            continue # Continue with the next video after delay
        except Exception as e:
            logger.error(f"处理 [{user_label}] 收藏视频 {aweme_id} 时发生意外错误: {str(e)}", exc_info=True)
            failed_saved += 1
        await asyncio.sleep(max(1, PROCESS_DELAY)) # Ensure delay between processing items

    logger.info(f"===== [{user_label}] 收藏视频处理完成 =====")
    logger.info(f"[{user_label}] 总计尝试: {total_to_process}, 成功/跳过: {processed_saved}, 失败: {failed_saved}")
    return failed_saved == 0

async def delete_message_after_delay(app, chat_id, message_id, delay_seconds):
    """
    延迟指定秒数后删除消息
    
    Args:
        app: Pyrogram客户端
        chat_id: 聊天ID
        message_id: 消息ID
        delay_seconds: 延迟秒数
    """
    try:
        await asyncio.sleep(delay_seconds)
        await app.delete_messages(chat_id, message_id)
        logger.info(f"已删除消息: {message_id} (延迟{delay_seconds}秒)")
    except Exception as e:
        logger.error(f"删除消息 {message_id} 失败: {str(e)}")

async def check_and_get_large_tasks():
    """
    检查并获取所有 large 任务
    
    Returns:
        list: large 任务列表
    """
    try:
        response = supabase.table("douyin") \
                           .select("aweme_id, sec_uid") \
                           .not_.is_("large", "null") \
                           .neq("large", "") \
                           .order('create_time', desc=True) \
                           .execute()
        
        return response.data or []
    except Exception as e:
        logger.error(f"查询 large 任务时发生错误: {str(e)}")
        return []

async def process_single_large_task(app, task):
    """
    处理单个 large 任务
    
    Returns:
        tuple: (是否成功, 是否遇到限流)
    """
    aweme_id = task.get("aweme_id")
    task_sec_uid = task.get("sec_uid")
    
    if not aweme_id:
        return False, False
    
    logger.info(f"处理 large 任务: {aweme_id}")
    
    try:
        author_info_for_task = {"sec_uid": task_sec_uid} if task_sec_uid else None
        task_ok = await process_single_aweme(
            app, aweme_id, current_author_info=author_info_for_task, 
            is_large_task_mode=True, is_full_scan=True
        )
        
        if task_ok:
            logger.info(f"Large 任务 {aweme_id} 处理成功")
            return True, False
        else:
            logger.warning(f"Large 任务 {aweme_id} 处理失败或未完成")
            return False, False
            
    except ConnectionError:
        logger.warning(f"处理 large 任务 {aweme_id} 时遭遇限流（ConnectionError）")
        return False, True
    except Exception as e:
        # 检查是否是限流错误
        if '429' in str(e) or 'rate limit' in str(e).lower():
            logger.warning(f"处理 large 任务 {aweme_id} 时遭遇限流: {str(e)}")
            return False, True
        logger.error(f"处理 large 任务 {aweme_id} 时发生错误: {str(e)}", exc_info=True)
        return False, False

# =============== 主循环入口 ===============
async def main_loop(app):
    logger.info("开始主循环")
    
    # 初始化全局端口管理 - 新策略：所有作者使用同一端口，失败时切换
    # 端口管理已通过全局变量 global_port_manager 实现
    
    # --- 检查should_fetch标记 ---
    should_fetch_profile = True  # 默认值
    try:
        response = supabase.table("does").select("should_fetch").execute()
        does_data = response.data
        if does_data and len(does_data) > 0:
            should_fetch_profile = does_data[0].get("should_fetch", True)
            logger.info(f"从does表获取should_fetch标记: {should_fetch_profile}")
    except Exception as e:
        logger.error(f"获取should_fetch标记失败: {e}，使用默认值True")
        should_fetch_profile = True

    # --- 检查是否需要处理点赞和收藏列表 ---
    try:
        # 查询does表中的douyinlikes字段
        response = supabase.table("does").select("douyinlikes").execute()
        does_data = response.data
        
        if does_data and does_data[0].get("douyinlikes") is True:
            logger.info("检测到douyinlikes标记为true，开始处理点赞和收藏任务...")
            
            # --- 处理点赞列表 (仅第一页) ---
            try:
                logger.info("处理 [Hulu] 点赞列表（仅第一页）...")
                await process_liked_videos(app, HULU_COOKIE, "hulu", "liked_by_hulu", is_full_scan=False)
                logger.info("[Hulu] 点赞列表处理阶段完成")
            except Exception as liked_err:
                logger.error(f"处理 [Hulu] 点赞列表时发生错误: {liked_err}", exc_info=True)
                await asyncio.sleep(3)

            try:
                logger.info("处理 [500] 点赞列表（仅第一页）...")
                await process_liked_videos(app, FIVE00_COOKIE, "500", "liked_by_500", is_full_scan=False)
                logger.info("[500] 点赞列表处理阶段完成")
            except Exception as liked_err:
                logger.error(f"处理 [500] 点赞列表时发生错误: {liked_err}", exc_info=True)
                await asyncio.sleep(3)

            # --- 处理收藏列表 (全量) ---
            try:
                logger.info("处理 [Hulu] 收藏列表（全量）...")
                await process_collected_videos(app, HULU_COOKIE, "hulu", "saved_by_hulu")
                logger.info("[Hulu] 收藏列表处理阶段完成")
            except Exception as saved_err:
                logger.error(f"处理 [Hulu] 收藏列表时发生错误: {saved_err}", exc_info=True)
                await asyncio.sleep(3)

            try:
                logger.info("处理 [500] 收藏列表（全量）...")
                await process_collected_videos(app, FIVE00_COOKIE, "500", "saved_by_500")
                logger.info("[500] 收藏列表处理阶段完成")
            except Exception as saved_err:
                logger.error(f"处理 [500] 收藏列表时发生错误: {saved_err}", exc_info=True)
                await asyncio.sleep(3)
                
            # 将douyinlikes标记设置为false
            try:
                supabase.table("does").update({"douyinlikes": False}).execute()
                logger.info("已将douyinlikes标记重置为false")
                
                # 点赞收藏任务完成后，主动切换到下一个端口
                old_port = get_current_port()
                new_port = switch_to_next_port()
                logger.info(f"点赞收藏任务完成，主动切换端口: 从 {old_port} 切换到 {new_port}")
                
                # --- 重新处理前2小时到前1小时内发布的作品 ---
                try:
                    logger.info("开始重新处理前4小时到前3小时内发布的作品...")
                    # 计算时间范围
                    three_hours_ago = datetime.now().replace(microsecond=0) - timedelta(hours=3)
                    four_hours_ago = datetime.now().replace(microsecond=0) - timedelta(hours=4)
                    three_hours_ago_str = three_hours_ago.isoformat()
                    four_hours_ago_str = four_hours_ago.isoformat()
                    
                    # 查询前4小时到前3小时内发布的作品
                    response = supabase.table("douyin") \
                                    .select("aweme_id, sec_uid, create_time") \
                                    .gte("create_time", four_hours_ago_str) \
                                    .lt("create_time", three_hours_ago_str) \
                                    .order('create_time', desc=True) \
                                    .execute()
                    recent_works = response.data or []
                    
                    if not recent_works:
                        logger.info("没有找到前4小时到前3小时内发布的作品")
                    else:
                        logger.info(f"找到 {len(recent_works)} 个前4小时到前3小时内发布的作品，准备重新处理")
                        processed_count = 0
                        failed_count = 0
                        
                        for work in recent_works:
                            aweme_id = work.get("aweme_id")
                            sec_uid = work.get("sec_uid")
                            create_time = work.get("create_time")
                            
                            if not aweme_id:
                                continue
                                
                            logger.info(f"重新处理时间范围内的作品: {aweme_id}, 发布时间: {create_time}")
                            try:
                                author_info_for_task = {"sec_uid": sec_uid} if sec_uid else None
                                # 创建一个自定义处理结果接收函数
                                custom_result_handler = True  # 标记使用自定义处理器

                                # 处理作品并获取file_id结果
                                result_string = await process_single_aweme(
                                    app, 
                                    aweme_id, 
                                    current_author_info=author_info_for_task, 
                                    is_full_scan=True,
                                    is_retry_failed=True,   # 标记为重试处理
                                    is_window_task=True     # 标记为窗口任务
                                )
                                
                                # 如果处理成功，获取file_id并存入high_quality字段
                                if result_string and isinstance(result_string, str) and "WINDOW_TASK" in result_string:
                                    # 从返回字符串中提取file_id
                                    # 格式: file_id1;file_id2;WINDOW_TASK 或 file_id1;file_id2;MUSIC_PROCESSED;WINDOW_TASK
                                    parts = result_string.split(';')
                                    file_ids = []
                                    for part in parts:
                                        if part not in ['WINDOW_TASK', 'MUSIC_PROCESSED', 'UPDATED']:
                                            file_ids.append(part)
                                    
                                    if file_ids:
                                        latest_file_id = ';'.join(file_ids)
                                        # 将file_id存入high_quality字段而不修改原file_id
                                        try:
                                            supabase.table("douyin") \
                                                  .update({"high_quality": latest_file_id}) \
                                                  .eq("aweme_id", aweme_id) \
                                                  .execute()
                                            logger.info(f"时间窗口内作品 {aweme_id} 的file_id已存入high_quality字段: {latest_file_id[:20]}...")
                                            processed_count += 1
                                        except Exception as hq_err:
                                            logger.error(f"更新作品 {aweme_id} 的high_quality字段失败: {hq_err}")
                                            failed_count += 1
                                    else:
                                        logger.warning(f"作品 {aweme_id} 处理成功但未找到最新的file_id")
                                        failed_count += 1
                                else:
                                    failed_count += 1
                                    logger.warning(f"重新处理作品 {aweme_id} 失败，无法获取file_id")
                            except ConnectionError:
                                logger.warning(f"重新处理作品 {aweme_id} 时遭遇限流，暂停 30 秒...")
                                failed_count += 1
                                await asyncio.sleep(30)
                                continue
                            except Exception as work_err:
                                logger.error(f"重新处理作品 {aweme_id} 时发生意外错误: {work_err}", exc_info=True)
                                failed_count += 1
                            
                            await asyncio.sleep(PROCESS_DELAY)
                        
                        logger.info(f"完成重新处理前4小时到前3小时内的作品，共 {len(recent_works)} 个，成功: {processed_count}，失败: {failed_count}")
                        
                except Exception as recent_work_err:
                    logger.error(f"重新处理前4小时到前3小时内发布作品时出错: {recent_work_err}", exc_info=True)
            except Exception as reset_err:
                logger.error(f"重置douyinlikes标记时出错: {reset_err}", exc_info=True)
        else:
            logger.info("douyinlikes标记为false，跳过点赞和收藏任务")
    except Exception as check_err:
        logger.error(f"检查douyinlikes标记时出错: {check_err}", exc_info=True)
        await asyncio.sleep(3)

    # --- 常规监控的作者 (增量) ---
    try:
        # 记录开始时间和统计数据
        monitoring_start_time = time.time()
        stats = {
            "total_authors": 0,
            "processed_authors": 0,
            "authors_with_new_posts": 0,
            "total_new_posts": 0,
            "processed_posts": 0,
            "successful_api_calls": 0,
            "failed_api_calls": 0
        }
        
        logger.info("开始常规监控作者处理")
        all_authors_processed = False
        page = 0
        
        while not all_authors_processed:
            # 分页获取作者列表
            authors_batch = await get_surveilled_authors(page=page, page_size=1000)
            
            if not authors_batch:
                logger.info("没有需要监控的作者，或已处理完所有作者")
                all_authors_processed = True
                break
            
            stats["total_authors"] += len(authors_batch)
            
            # 逐个处理作者，而不是批量处理
            for author in authors_batch:
                author_name = author.get('nickname') or author.get('unique_id') or author.get('sec_uid')
                sec_uid = author.get('sec_uid')
                
                # 获取当前全局使用的端口
                current_port = get_current_port()
                
                logger.info(f"🔄 开始处理作者: {author_name} (sec_uid={sec_uid}) | 当前API端口: {current_port}")
                stats["processed_authors"] += 1
                
                # 更新作者最后获取时间
                await update_author_last_fetched_at(sec_uid)
                
                try:
                    # 当 author.get('once') is True 时, 直接调用 process_author_works，它会处理资料更新和作品。
                    # 当 author.get('once') is False 时, 仅更新资料。
                    if author.get('once') is True:
                        logger.info(f"作者 {author_name} (once=True)，调用 process_author_works 进行增量作品处理")
                        result = await process_author_works(app, author, is_full_scan=False, should_fetch_profile=should_fetch_profile)
                        
                        if result.get("ok"):
                            # 更新统计数据
                            stats["successful_api_calls"] += 1 # 假设 process_author_works 的调用视为一次成功的"主"API协调
                            if result.get("processed", 0) > 0:
                                stats["authors_with_new_posts"] += 1
                                stats["total_new_posts"] += result.get("processed", 0)
                                stats["processed_posts"] += result.get("processed", 0)
                            logger.info(f"作者 {author_name} 处理完成。新作品: {result.get('processed',0)}, 失败: {result.get('failed',0)}")
                        else:
                            stats["failed_api_calls"] += 1
                            logger.error(f"处理作者 {author_name} (once=True) 时 process_author_works 返回错误: {result.get('error')}")
                    else:
                        # author.get('once') is False, 仅更新用户资料
                        if should_fetch_profile:
                            logger.info(f"作者 {author_name} (once=False)，仅更新用户资料")
                            profile_result = await fetch_user_profile_api(sec_uid)
                            if isinstance(profile_result, Exception):
                                logger.error(f"获取作者 {author_name} 个人资料时出错: {str(profile_result)}")
                                stats["failed_api_calls"] += 1
                                continue # 继续下一个作者
                            
                            if not profile_result or not profile_result.get("data"):
                                 logger.error(f"作者 {author_name}: 获取用户资料API失败，跳过处理")
                                 stats["failed_api_calls"] += 1
                                 continue
                                 
                            if not profile_result.get("data", {}).get("user"):
                                 logger.warning(f"获取作者 {author_name} 的个人资料为空或无效，跳过更新")
                                 stats["failed_api_calls"] += 1
                                 continue

                            stats["successful_api_calls"] += 1
                            user_json = profile_result.get("data", {}).get("user", {})
                            live_payload = build_live_payload(user_json)
                            upsert_live_info_to_db(user_json, live_payload)
                            logger.info(f"已更新作者资料: {author_name}")
                        else:
                            logger.info(f"should_fetch_profile=False，跳过作者 {author_name} (once=False) 的资料获取")
                
                except Exception as author_err:
                    logger.error(f"处理作者 {author_name} 流程时出错: {str(author_err)}", exc_info=True)
                    stats["failed_api_calls"] += 1 # 统计在处理单个作者时的顶层异常
                
                # 处理完作者后，查询 large 任务
                large_tasks = await check_and_get_large_tasks()
                
                # 如果有 large 任务，立即处理
                if large_tasks:
                    logger.info(f"发现 {len(large_tasks)} 个待处理的 large 任务")
                    for task in large_tasks:
                        try:
                            success, hit_rate_limit = await process_single_large_task(app, task)
                            if hit_rate_limit:
                                logger.warning(f"处理 large 任务时遭遇限流，暂停 60 秒...")
                                await asyncio.sleep(60)
                                break  # 遇到限流就停止处理剩余的 large 任务
                            elif success:
                                # 成功处理一个任务后的小延迟
                                await asyncio.sleep(PROCESS_DELAY)
                        except Exception as e:
                            logger.error(f"处理 large 任务时出错: {str(e)}")
                
                # 移除作者间的固定等待时间
                # await asyncio.sleep(3)
            
            # 如果当前页获取的作者数不足1000，说明已经处理完所有作者
            if len(authors_batch) < 1000:
                all_authors_processed = True
            else:
                page += 1
                # 不再需要页面间隔
        
        # 计算并记录总用时
        monitoring_end_time = time.time()
        monitoring_duration = monitoring_end_time - monitoring_start_time
        
        # 准备发送统计信息到Telegram
        stats_message = f"""
📊 常规监控统计报告 📊
⏱️ 总用时: {monitoring_duration:.2f} 秒
👥 扫描作者: {stats['total_authors']} 人
🔍 处理作者: {stats['processed_authors']} 人
✨ 有新作品作者: {stats['authors_with_new_posts']} 人
📝 新作品数量: {stats['total_new_posts']} 个
✅ 处理成功: {stats['processed_posts']} 个
🌐 API请求成功: {stats['successful_api_calls']} 次
❌ API请求失败: {stats['failed_api_calls']} 次
⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 发送统计信息到Telegram
        while True:
            try:
                message = await app.send_message(TELEGRAM_CHAT_ID, stats_message)
                # 不再删除消息，保留统计报告
                # if message:
                #     asyncio.create_task(delete_message_after_delay(app, TELEGRAM_CHAT_ID, message.id, 120))
                break  # 成功发送，退出循环
            except FloodWait as e:
                wait_time = e.value
                logger.warning(f"Telegram API FloodWait: 要求等待 {wait_time} 秒. 将等待 {wait_time + 1} 秒后重试发送统计信息...")
                await asyncio.sleep(wait_time + 1)
                # 继续循环重试
            except Exception as e:
                logger.error(f"发送统计信息到Telegram失败: {str(e)}")
                break  # 其他错误不重试
        
        logger.info("常规作者监控处理完成")
    except Exception as e:
        logger.error(f"常规作者监控循环出错: {str(e)}", exc_info=True)


    logger.info("本轮主循环结束")

async def main():
    logger.info("抖音作者监控系统启动")
    os.makedirs('downloads', exist_ok=True)

    global bot_username
    global primary_bot
    primary_bot = Client(
        "douyin_surveillance_bot",
        api_id=API_ID,
        api_hash=API_HASH,
        bot_token=BOT_TOKEN
    )

    try:
        await primary_bot.start()
        logger.info("Bot 启动成功")

        try:
            me = await primary_bot.get_me()
            bot_username = me.username
            logger.info(f"Bot 连接成功: @{bot_username}")
        except Exception as e:
            logger.error(f"获取 Bot 信息失败: {e}")
            bot_username = ""

        logger.info("抖音作者监控系统已启动")

        while True:
            await main_loop(primary_bot)
            logger.info(f"下一轮循环将在 {SCAN_INTERVAL} 秒后开始...")
            await asyncio.sleep(SCAN_INTERVAL)

    except asyncio.CancelledError:
        logger.info("接收到中断信号，程序准备退出...")
    except Exception as e:
        logger.error(f"主程序发生致命错误: {str(e)}", exc_info=True)
    finally:
        if primary_bot and primary_bot.is_connected:
            await primary_bot.stop()
        logger.info("Bot 已停止，程序已关闭")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("检测到 Ctrl+C，正在关闭...")
