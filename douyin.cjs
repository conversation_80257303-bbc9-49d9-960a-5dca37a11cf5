// bot.js - 抖音下载 Telegram Bot - 第一部分
const { Telegraf, Markup } = require('telegraf');
const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');
const fs = require('fs/promises');
const fsSync = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
// 添加dotenv加载环境变量
require('dotenv').config();

// =============== 内置类定义 ===============
const EventEmitter = require('events');

class TelegramQueue extends EventEmitter {
  constructor(bot, options = {}) {
    super();
    this.bot = bot;
    this.options = {
      rateLimitDelay: options.rateLimitDelay || 100, // 默认100ms间隔
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000,
      queueSize: options.queueSize || 1000,
      ...options
    };

    this.queue = [];
    this.isProcessing = false;
    this.stats = {
      sent: 0,
      failed: 0,
      queued: 0
    };
  }

  // 添加消息到队列
  async enqueue(messageData) {
    if (this.queue.length >= this.options.queueSize) {
      throw new Error('消息队列已满');
    }

    const message = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      data: messageData,
      retryCount: 0,
      createdAt: Date.now(),
      priority: messageData.priority || 0
    };

    // 根据优先级插入队列
    const insertIndex = this.queue.findIndex(m => m.priority < message.priority);
    if (insertIndex === -1) {
      this.queue.push(message);
    } else {
      this.queue.splice(insertIndex, 0, message);
    }

    this.stats.queued++;
    this.emit('message-queued', message);

    // 如果当前没有在处理，开始处理队列
    if (!this.isProcessing) {
      this.processQueue();
    }

    return message.id;
  }

  // 处理队列
  async processQueue() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const message = this.queue.shift();
      
      try {
        await this.sendMessage(message);
        this.stats.sent++;
        this.emit('message-sent', message);
        
        // 速率限制
        await this.delay(this.options.rateLimitDelay);
      } catch (error) {
        await this.handleError(message, error);
      }
    }

    this.isProcessing = false;
  }

  // 发送消息
  async sendMessage(message) {
    const { type, chatId, ...params } = message.data;

    switch (type) {
      case 'text':
        return await this.bot.telegram.sendMessage(chatId, params.text, params.options);
      
      case 'photo':
        return await this.bot.telegram.sendPhoto(chatId, params.photo, params.options);
      
      case 'video':
        return await this.bot.telegram.sendVideo(chatId, params.video, params.options);
      
      case 'document':
        return await this.bot.telegram.sendDocument(chatId, params.document, params.options);
      
      case 'audio':
        return await this.bot.telegram.sendAudio(chatId, params.audio, params.options);
      
      case 'mediaGroup':
        return await this.bot.telegram.sendMediaGroup(chatId, params.media, params.options);
      
      case 'editMessageText':
        return await this.bot.telegram.editMessageText(
          params.chatId || chatId,
          params.messageId,
          params.inlineMessageId,
          params.text,
          params.options
        );
      
      case 'deleteMessage':
        return await this.bot.telegram.deleteMessage(chatId, params.messageId);
      
      default:
        throw new Error(`未知的消息类型: ${type}`);
    }
  }

  // 处理错误
  async handleError(message, error) {
    console.error(`发送消息失败:`, error);
    
    // 检查是否是速率限制错误
    if (error.response && error.response.error_code === 429) {
      const retryAfter = error.response.parameters?.retry_after || 30;
      console.log(`触发速率限制，${retryAfter}秒后重试`);
      
      // 将消息重新加入队列前端
      this.queue.unshift(message);
      
      // 暂停处理
      await this.delay(retryAfter * 1000);
      return;
    }

    // 检查是否需要重试
    if (message.retryCount < this.options.maxRetries) {
      message.retryCount++;
      console.log(`重试消息 ${message.id}，第 ${message.retryCount} 次`);
      
      // 延迟后重新加入队列
      setTimeout(() => {
        this.queue.push(message);
        if (!this.isProcessing) {
          this.processQueue();
        }
      }, this.options.retryDelay * message.retryCount);
    } else {
      this.stats.failed++;
      this.emit('message-failed', { message, error });
    }
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 发送文本消息
  async sendText(chatId, text, options = {}) {
    return this.enqueue({
      type: 'text',
      chatId,
      text,
      options,
      priority: options.priority || 0
    });
  }

  // 发送图片
  async sendPhoto(chatId, photo, options = {}) {
    return this.enqueue({
      type: 'photo',
      chatId,
      photo,
      options,
      priority: options.priority || 0
    });
  }

  // 发送视频
  async sendVideo(chatId, video, options = {}) {
    return this.enqueue({
      type: 'video',
      chatId,
      video,
      options,
      priority: options.priority || 0
    });
  }

  // 发送文档
  async sendDocument(chatId, document, options = {}) {
    return this.enqueue({
      type: 'document',
      chatId,
      document,
      options,
      priority: options.priority || 0
    });
  }

  // 发送音频
  async sendAudio(chatId, audio, options = {}) {
    return this.enqueue({
      type: 'audio',
      chatId,
      audio,
      options,
      priority: options.priority || 0
    });
  }

  // 发送媒体组
  async sendMediaGroup(chatId, media, options = {}) {
    return this.enqueue({
      type: 'mediaGroup',
      chatId,
      media,
      options,
      priority: options.priority || 0
    });
  }

  // 编辑消息文本
  async editMessageText(chatId, messageId, text, options = {}) {
    return this.enqueue({
      type: 'editMessageText',
      chatId,
      messageId,
      text,
      options,
      priority: options.priority || 1 // 编辑消息优先级稍高
    });
  }

  // 删除消息
  async deleteMessage(chatId, messageId, options = {}) {
    return this.enqueue({
      type: 'deleteMessage',
      chatId,
      messageId,
      priority: options.priority || 0
    });
  }

  // 获取队列状态
  getStats() {
    return {
      ...this.stats,
      queueLength: this.queue.length,
      isProcessing: this.isProcessing
    };
  }

  // 清空队列
  clear() {
    this.queue = [];
    this.stats.queued = 0;
  }
}

// PriorityTaskQueue - 优先级任务队列（简化版，不使用RabbitMQ）
class PriorityTaskQueue extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 5000,
      ...options
    };
    this.queue = [];
    this.isProcessing = false;
  }

  async enqueue(task, priority = 0) {
    const taskItem = {
      id: task.id || `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      data: task,
      priority,
      retryCount: 0,
      createdAt: Date.now()
    };

    // 根据优先级插入队列
    const insertIndex = this.queue.findIndex(t => t.priority < taskItem.priority);
    if (insertIndex === -1) {
      this.queue.push(taskItem);
    } else {
      this.queue.splice(insertIndex, 0, taskItem);
    }

    this.emit('task-enqueued', taskItem);
    return taskItem.id;
  }

  async process(handler) {
    if (this.isProcessing) return;
    this.isProcessing = true;

    while (this.queue.length > 0) {
      const task = this.queue.shift();
      
      try {
        await handler(task.data);
        this.emit('task-completed', task);
      } catch (error) {
        if (task.retryCount < this.options.maxRetries) {
          task.retryCount++;
          setTimeout(() => {
            this.queue.push(task);
            this.process(handler);
          }, this.options.retryDelay * task.retryCount);
        } else {
          this.emit('task-failed', { task, error });
        }
      }
    }

    this.isProcessing = false;
  }

  getQueueLength() {
    return this.queue.length;
  }

  clear() {
    this.queue = [];
  }
}

/**
 * 环境变量说明:
 * 
 * SUPABASE_URL - Supabase数据库URL
 * SUPABASE_KEY - Supabase匿名密钥
 * BUTHISBOT - Telegram Bot Token
 * TELEGRAM_CHAT_ID - 存储频道ID，用于保存媒体文件
 * API_BASE_URL - API基本URL，默认为http://localhost:8080
 * 
 * 其他可选的环境变量:
 * TIKHUB_API_KEY - TikHub API密钥
 */

// =============== 环境变量校验 ===============
function checkRequiredEnvVars() {
  const requiredVars = [
    'SUPABASE_URL', 
    'SUPABASE_KEY',
    'BUTHISBOT',
    'TELEGRAM_CHAT_ID'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('错误: 以下必要的环境变量未设置:');
    missingVars.forEach(varName => console.error(`- ${varName}`));
    console.error('请在.env文件中设置这些变量');
    process.exit(1);
  }
}

// 执行环境变量检查
checkRequiredEnvVars();

// =============== 日志配置 ===============
const logger = {
  info: (message) => console.log(`[${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai', hour12: false })}] INFO: ${message}`),
  error: (message, error) => console.error(`[${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai', hour12: false })}] ERROR: ${message}`, error),
  warning: (message, error) => console.warn(`[${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai', hour12: false })}] WARNING: ${message}`, error)
};

// =============== Supabase 配置 ===============
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('错误: SUPABASE_URL 或 SUPABASE_KEY 环境变量未设置');
  console.error('SUPABASE_URL exists:', !!SUPABASE_URL);
  console.error('SUPABASE_KEY exists:', !!SUPABASE_KEY);
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// =============== 全局变量 ===============
const DELETE_FILES = true;
const STORAGE_CHANNEL_ID = parseInt(process.env.TELEGRAM_CHAT_ID);
const RETRY_CACHE = {};
const MUSIC_CACHE = {};
const API_BASE_URL = process.env.API_BASE_URL || "http://localhost:8080";

// 备用 bot tokens（仅用于发送到 channel）
const BACKUP_BOT_TOKENS = [
  process.env.BOT_TOKEN,
  process.env.BOT_TOKEN_SECONDARY
].filter(token => token); // 过滤掉未定义的 token

// 检查启动参数是否包含 'live'
const SHOW_LIVE_BUTTONS = process.argv.includes('live');

// =============== Worker池和队列配置 ===============
let workerPool = null;
let taskQueue = null;
let telegramQueue = null;
let realtimeChannel = null;

// Worker系统已禁用
// async function initializeWorkerSystem(bot) {
//   try {
//     // 初始化Worker池
//     workerPool = new WorkerPool({
//       minWorkers: 2,
//       maxWorkers: 4,
//       taskTimeout: 300000, // 5分钟任务超时
//       idleTimeout: 60000   // 1分钟空闲超时
//     });
// 
//     // 初始化任务队列（简化版）
//     taskQueue = new PriorityTaskQueue({
//       maxRetries: 3,
//       retryDelay: 5000
//     });
    
//     // 设置任务处理器
//     taskQueue.on('task-enqueued', (task) => {
//       logger.info(`任务入队: ${task.id}`);
//     });
//     
//     taskQueue.on('task-completed', (task) => {
//       logger.info(`任务完成: ${task.id}`);
//     });
//     
//     taskQueue.on('task-failed', ({ task, error }) => {
//       logger.error(`任务失败: ${task.id}, 错误: ${error.message}`);
//     });
// 
//     // 初始化Telegram消息队列
//     telegramQueue = new TelegramQueue(bot, {
//       rateLimitDelay: 100,  // 100ms间隔
//       maxRetries: 3,
//       queueSize: 1000
//     });

//     // 监听Worker池事件
//     workerPool.on('task-complete', (data) => {
//       logger.info(`任务完成: ${data.taskId}, 耗时: ${data.processingTime}ms`);
//     });
// 
//     workerPool.on('task-error', (data) => {
//       logger.error(`任务错误: ${data.taskId}, 错误: ${data.error}`);
//     });
// 
//     workerPool.on('task-progress', (data) => {
//       logger.info(`任务进度: ${data.taskId}, ${data.message}`);
//     });
// 
//     logger.info('Worker系统初始化完成');
//   } catch (error) {
//     logger.error('初始化Worker系统失败:', error);
//     throw error;
//   }
// }

// 格式化为北京时间 - 函数已移至第1082行，使用更完善的版本


// isUserSubscribedToAuthor 函数已在文件开头定义

// =============== 抖音解析器 ===============
class DouyinParser {
  static _sortBitrateItems(bitRateList) {
    // Sort by resolution, FPS, and bitrate
    return bitRateList.sort((a, b) => {
      const aPlayAddr = a.play_addr || {};
      const bPlayAddr = b.play_addr || {};
      const aResolution = (aPlayAddr.width || 0) * (aPlayAddr.height || 0);
      const bResolution = (bPlayAddr.width || 0) * (bPlayAddr.height || 0);
      
      if (aResolution !== bResolution) return bResolution - aResolution;
      
      const aFps = a.FPS || 0;
      const bFps = b.FPS || 0;
      if (aFps !== bFps) return bFps - aFps;
      
      const aBitrate = a.bit_rate || 0;
      const bBitrate = b.bit_rate || 0;
      return bBitrate - aBitrate;
    });
  }

  static _getFirstUrl(urlData) {
    const urlList = urlData?.url_list || [];
    if (Array.isArray(urlList) && urlList.length > 0) {
      return urlList[0];
    }
    return "";
  }

  static _getBestPlayUrl(videoInfo) {
    const bitRateList = videoInfo?.bit_rate || [];
    if (bitRateList.length > 0) {
      const sortedItems = DouyinParser._sortBitrateItems(bitRateList);
      for (const item of sortedItems) {
        const playAddr = item.play_addr || {};
        const bestUrl = DouyinParser._getFirstUrl(playAddr);
        if (bestUrl && !bestUrl.includes("watermark")) {
          return bestUrl;
        }
      }
    }

    // Fallback to default play_addr
    const fallbackUrl = DouyinParser._getFirstUrl(videoInfo?.play_addr || {});
    if (fallbackUrl && !fallbackUrl.includes("watermark")) {
      return fallbackUrl;
    }
    return fallbackUrl;
  }

  static _getBestImageUrl(imgData) {
    const urlList = imgData?.url_list || [];
    // Prefer URLs without "water" (watermark)
    const noWaterUrls = urlList.filter(u => !u.includes("water"));
    if (noWaterUrls.length > 0) {
      return noWaterUrls[0];
    }
    if (urlList.length > 0) {
      return urlList[0];
    }
    return "";
  }

  static parseAweme(respData) {
    try {
      const data = respData?.data || {};
      if (typeof data !== 'object') {
        return {};
      }

      const awemeId = data.aweme_id || "";
      const desc = data.desc || "";
      const createTimeTs = data.create_time || 0;
      
      let createTimeStr = "";
      try {
        createTimeStr = new Date(createTimeTs * 1000).toISOString()
          .replace('T', ' ').slice(0, 19);
      } catch (e) {}

      const author = data.author || {};
      const authorInfo = {
        nickname: author.nickname || "",
        uid: author.uid || "",
        sec_uid: author.sec_uid || "",
        unique_id: author.unique_id || "",
        follower_count: author.follower_count || 0,
        total_favorited: author.total_favorited || 0,
      };

      // Location
      let locationInfo = {};
      const anchorInfo = data.anchor_info || {};
      try {
        if (typeof anchorInfo.extra === 'string') {
          const extraData = JSON.parse(anchorInfo.extra);
          if (typeof extraData === 'object') {
            const addressInfo = extraData.address_info || {};
            locationInfo = {
              province: addressInfo.province || "",
              city: addressInfo.city || ""
            };
          }
        }
      } catch (e) {}

      // Statistics
      const statistics = data.statistics || {};
      const stats = {
        comment_count: statistics.comment_count || 0,
        digg_count: statistics.digg_count || 0,
        collect_count: statistics.collect_count || 0,
        share_count: statistics.share_count || 0
      };

      // Music
      const music = data.music || {};
      const musicInfo = {
        title: music.title || "",
        author: music.author || "",
        play_url: DouyinParser._getFirstUrl(music.play_url || {})
      };

      // Media
      const images = data.images || [];
      const videoInfo = data.video || {};
      const durationMs = data.duration || 0;
      const durationS = durationMs / 1000.0;

      let mediaInfo;
      if (images.length > 0) {
        // Image collection
        mediaInfo = {
          cover_url: "",
          play_url: "",
          width: 0,
          height: 0,
          duration: durationS,
          images: []
        };
        
        for (const img of images) {
          const imgInfo = {
            url: DouyinParser._getBestImageUrl(img),
            width: img.width || 0,
            height: img.height || 0,
            video: null
          };
          
          const vidData = img.video;
          if (vidData && typeof vidData === 'object') {
            const bestPlayUrl = DouyinParser._getBestPlayUrl(vidData);
            imgInfo.video = {
              url: bestPlayUrl,
              width: vidData.width || 0,
              height: vidData.height || 0
            };
          }
          
          mediaInfo.images.push(imgInfo);
        }
      } else {
        // Regular video
        mediaInfo = {
          cover_url: DouyinParser._getFirstUrl(videoInfo.cover || {}),
          play_url: DouyinParser._getBestPlayUrl(videoInfo),
          width: videoInfo.width || 0,
          height: videoInfo.height || 0,
          duration: durationS,
          images: []
        };
      }

      return {
        base_info: {
          aweme_id: awemeId,
          desc: desc,
          create_time: createTimeStr
        },
        author_info: authorInfo,
        location_info: locationInfo,
        statistics: stats,
        music_info: musicInfo,
        media_info: mediaInfo
      };
    } catch (error) {
      logger.error("parse_aweme error:", error);
      return {};
    }
  }

  static parseUser(respData) {
    try {
      const data = respData?.data || {};
      if (typeof data !== 'object') {
        return null;
      }
      
      const author = data.author || {};
      if (!author || !author.uid || !author.sec_uid) {
        return null;
      }
      
      const userData = {
        uid: author.uid,
        sec_uid: author.sec_uid,
        short_id: author.short_id || null,
        unique_id: author.unique_id || null,
        
        nickname: author.nickname || null,
        signature: author.signature || null,
        user_age: author.user_age || null,
        avatar_thumb_uri: author.avatar_thumb?.uri || null,
        avatar_thumb_url: author.avatar_thumb?.url_list?.[0] || null,
        create_time: author.create_time 
          ? new Date(author.create_time * 1000).toISOString() 
          : null,
        
        follower_count: author.follower_count || 0,
        following_count: author.following_count || 0,
        total_favorited: author.total_favorited || 0,
        favoriting_count: author.favoriting_count || 0,
        
        status: author.status || 1,
        verification_type: author.verification_type || null,
        user_canceled: author.user_canceled || false,
        mate_add_permission: author.mate_add_permission || null,
        
        custom_verify: author.custom_verify || null,
        enterprise_verify_reason: author.enterprise_verify_reason || null,
        
        prevent_download: author.prevent_download || false,
        contacts_status: author.contacts_status || null,
        
        cover_url: author.cover_url?.[0]?.url_list?.[0] || null,
        
        last_fetched_at: new Date().toISOString()
      };
      
      return userData;
    } catch (error) {
      logger.error("parse_user error:", error);
      return null;
    }
  }
}

// =============== 解析和下载函数 ===============
function parseDouyinWork(respData) {
  return DouyinParser.parseAweme(respData);
}

function parseDouyinUser(respData) {
  return DouyinParser.parseUser(respData);
}

// 端口故障转移辅助函数
async function callWithPortFailover(makeUrl, fetchOptions = {}) {
  const fallbackPorts = [8081, 8082, 8083];
  let lastError = null;
  
  // 首先尝试默认端口（8080）
  try {
    const defaultUrl = makeUrl("http://localhost:8080");
    logger.info(`尝试默认端口 8080: ${defaultUrl}`);
    const response = await fetch(defaultUrl, fetchOptions);
    return { response, url: defaultUrl };
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      logger.warning(`端口 8080 连接被拒绝，尝试备用端口...`);
      lastError = error;
    } else {
      throw error; // 非连接错误直接抛出
    }
  }
  
  // 尝试备用端口
  for (const port of fallbackPorts) {
    try {
      const fallbackUrl = makeUrl(`http://localhost:${port}`);
      logger.info(`尝试备用端口 ${port}: ${fallbackUrl}`);
      const response = await fetch(fallbackUrl, fetchOptions);
      logger.info(`成功使用备用端口 ${port}`);
      return { response, url: fallbackUrl };
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        logger.warning(`端口 ${port} 连接被拒绝`);
        lastError = error;
        continue;
      } else {
        throw error; // 非连接错误直接抛出
      }
    }
  }
  
  // 所有端口都失败
  logger.error(`所有端口（8080, ${fallbackPorts.join(', ')}）都无法连接`);
  throw lastError;
}

// 在API调用函数中添加重试机制的通用函数
async function retryApiCallWithBackoff(apiCall, maxRetries = 3, initialBackoffMs = 1000) {
  let retries = 0;
  let backoffTime = initialBackoffMs;
  
  while (retries < maxRetries) {
    try {
      return await apiCall();
    } catch (error) {
      const is429 = error.status === 429 
                    || (error.message && error.message.includes('429')) 
                    || (error.message && error.message.toLowerCase().includes('too many requests'));
      
      if (is429 && retries < maxRetries - 1) {
        retries++;
        logger.warning(`API限流(429)，第${retries}次重试，等待${backoffTime/1000}秒...`);
        await new Promise(resolve => setTimeout(resolve, backoffTime));
        backoffTime *= 2;
      } else {
        throw error;
      }
    }
  }
}

// =============== 数据库保存函数 ===============

// 新版本的 saveDouyinToDatabase
async function saveDouyinToDatabase(parsedData) {
  try {
    const baseInfo = parsedData?.base_info || {};
    const authorInfo = parsedData?.author_info || {};
    const locationInfo = parsedData?.location_info || {};
    const musicInfo = parsedData?.music_info || {};
    const mediaInfo = parsedData?.media_info || {};
    
    const data = {
      aweme_id: baseInfo.aweme_id || null,
      description: baseInfo.desc || null,
      create_time: baseInfo.create_time || null,
      nickname: authorInfo.nickname || null,
      uid: authorInfo.uid || null,
      sec_uid: authorInfo.sec_uid || null,
      unique_id: authorInfo.unique_id || null,
      follower_count: authorInfo.follower_count || 0,
      total_favorited: authorInfo.total_favorited || 0,
      province: locationInfo.province || null,
      city: locationInfo.city || null,
      music_title: musicInfo.title || null,
      music_author: musicInfo.author || null,
      music_play_url: musicInfo.play_url || null,
      cover_url: mediaInfo.cover_url || null,
      media_play_url: mediaInfo.play_url || null,
      duration: mediaInfo.duration || 0,
      file_id: null,
      large: null,
      bot_token: process.env.BUTHISBOT
    };
    
    if (!data.aweme_id) {
      logger.error("保存数据库失败: 缺少作品ID");
      return false;
    }
    
    const { error: insertError } = await supabase
      .from("douyin")
      .insert([data]);
    
    if (insertError) {
      // 如果已经存在，这里一般是主键冲突
      if (insertError.code === '23505') {
        logger.warning(`尝试插入已存在的作品 ${data.aweme_id}，跳过插入。`);
        return true;
      } else {
        logger.error(`保存作品 ${data.aweme_id} 到数据库失败: ${insertError.message}`, insertError);
        return false;
      }
    }
    
    logger.info(`成功保存作品 ${data.aweme_id} 到数据库`);
    return true;
  } catch (error) {
    logger.error(`保存数据库异常: ${error.message}`, error);
    return false;
  }
}

// 单纯保存用户数据
async function saveDouyinUserToDatabase(userData) {
  try {
    if (!userData || !userData.uid) {
      logger.error("保存用户数据失败: 缺少必要字段uid");
      return false;
    }
    
    // sec_uid可能为空，但uid是必须的
    if (!userData.sec_uid) {
      logger.warning(`保存用户数据时缺少sec_uid，但将继续保存 (uid: ${userData.uid})`);
    }
    
    // 先查询是否存在记录
    const { data: existingUser, error: selectError } = await supabase
      .from("douyin_user")
      .select("uid")
      .eq("uid", userData.uid)
      .single();
    
    if (selectError && selectError.code !== 'PGRST116') {
      logger.error(`查询现有用户数据失败: ${selectError.message}`, selectError);
    }
    
    // 清理userData，移除所有值为undefined的字段
    const cleanedData = {};
    for (const [key, value] of Object.entries(userData)) {
      if (value !== undefined) {
        cleanedData[key] = value;
      }
    }
    
    if (existingUser) {
      // 用户已存在，跳过更新
      logger.info(`用户 ${userData.uid} (${userData.nickname || "未知昵称"}) 已存在，跳过更新`);
      return true;
    } else {
      // 创建模式：只插入API提供的数据，其余使用数据库默认值
      // 移除手动设置的默认值（如 once: false）
      if ('once' in cleanedData && !userData.hasOwnProperty('once')) {
        delete cleanedData.once;
      }
      
      const { error } = await supabase
        .from("douyin_user")
        .insert(cleanedData);
      
      if (error) {
        logger.error(`创建用户数据失败: ${error.message}`, error);
        return false;
      }
      
      logger.info(`成功创建用户 ${userData.uid} (${userData.nickname || "未知昵称"}) 的记录`);
    }
    
    return true;
  } catch (error) {
    logger.error(`保存用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// 去掉了对 profileData 的处理，这个函数现在仅存为合并基础数据示例
async function saveEnhancedUserToDatabase(baseUserData) {
  try {
    if (!baseUserData || !baseUserData.uid || !baseUserData.sec_uid) {
      logger.error("保存增强用户数据失败: 缺少必要的基础字段uid或sec_uid");
      return false;
    }
    
    // 先查询是否存在记录
    const { data: existingUser, error: selectError } = await supabase
      .from("douyin_user")
      .select("uid")
      .eq("uid", baseUserData.uid)
      .single();
    
    if (selectError && selectError.code !== 'PGRST116') {
      logger.error(`查询现有用户数据失败: ${selectError.message}`, selectError);
    }
    
    // 清理baseUserData，移除所有值为undefined的字段
    const cleanedData = {};
    for (const [key, value] of Object.entries(baseUserData)) {
      if (value !== undefined) {
        cleanedData[key] = value;
      }
    }
    
    if (existingUser) {
      // 用户已存在，跳过更新
      logger.info(`用户 ${baseUserData.uid} (${baseUserData.nickname || "未知昵称"}) 已存在，跳过更新`);
      return true;
    } else {
      // 创建模式：只插入API提供的数据，其余使用数据库默认值
      const { error } = await supabase
        .from("douyin_user")
        .insert(cleanedData);
      
      if (error) {
        logger.error(`创建增强用户数据失败: ${error.message}`, error);
        return false;
      }
      
      logger.info(`成功创建用户 ${baseUserData.uid} (${baseUserData.nickname || "未知昵称"}) 的增强记录`);
    }
    
    return true;
  } catch (error) {
    logger.error(`保存增强用户数据库异常: ${error.message}`, error);
    return false;
  }
}

// =============== URL解析函数 ===============
function extractDouyinVideoUrl(text) {
  const pattern = /(https?:\/\/v\.douyin\.com\/\S+|https?:\/\/www\.douyin\.com\/video\/\S+|https?:\/\/www\.douyin\.com\/note\/\S+|https?:\/\/www\.douyin\.com\/lvdetail\/\S+)/;
  const match = text.match(pattern);
  return match ? match[1].trim() : "";
}

function extractDouyinUserUrl(text) {
  const pattern = /(https:\/\/www\.douyin\.com\/user\/[^\s]+)/;
  const match = text.match(pattern);
  return match ? match[1].trim() : "";
}

function extractSecUserIdFromUserUrl(url) {
  const pattern = /https:\/\/www\.douyin\.com\/user\/([^/?]+)/;
  const match = url.match(pattern);
  return match ? match[1] : "";
}

// =============== 抖音API调用函数（剩余可用的） ===============
async function fetchOneVideoApi(awemeId) {
  return retryApiCallWithBackoff(async () => {
    try {
      const douyinUrl = `https://www.douyin.com/video/${awemeId}`;
      const params = new URLSearchParams({
        url: douyinUrl,
        minimal: "false"
      });
      
      // 使用端口故障转移
      const { response, url: actualUrl } = await callWithPortFailover(
        (baseUrl) => `${baseUrl}/api/hybrid/video_data?${params}`,
        { timeout: 30000 }
      );
      
      logger.info(`[fetchOneVideoApi] 使用API URL: ${actualUrl}`);
      
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const jsonData = await response.json();
      
      const isNotAvailable = (
        !jsonData 
        || !jsonData.data 
        || jsonData.status_code === 404
        || jsonData.status_code === 10000
        || (jsonData.data && jsonData.data.status_code === 404)
        || (jsonData.data && jsonData.data.error_code === 10000)
        || (jsonData.data && jsonData.data.aweme_id === undefined)
      );
      
      if (isNotAvailable) {
        logger.warning(`检测到不可用作品: ${awemeId}，将标记为not available`);
        const minimalData = {
          base_info: { aweme_id: awemeId, desc: "Not Available" },
          author_info: {},
          location_info: {},
          music_info: {},
          media_info: {}
        };
        await saveDouyinToDatabase(minimalData);
        
        return { 
          data: { aweme_id: awemeId },
          not_available: true,
          status_message: "作品不可用或已删除"
        };
      }
      
      return jsonData;
    } catch (error) {
      logger.error(`fetch_one_video_api error: ${error.message}`, error);
      throw error;
    }
  }, 3, 2000);
}

// 获取用户作品数量
async function fetchUserAwemeCount(secUid) {
  try {
    logger.info(`获取用户作品数量，sec_uid: ${secUid}`);
    
    // 使用端口故障转移
    const { response, url: actualUrl } = await callWithPortFailover(
      (baseUrl) => `${baseUrl}/api/douyin/web/handler_user_profile?sec_user_id=${secUid}`,
      { timeout: 30000 }
    );
    
    logger.info(`获取用户作品数量使用的API: ${actualUrl}`);
    
    if (!response.ok) {
      logger.error(`获取用户资料失败: HTTP ${response.status}`);
      return null;
    }
    
    const data = await response.json();
    
    // 从返回数据中提取 aweme_count
    if (data && data.data && data.data.user) {
      const awemeCount = data.data.user.aweme_count;
      logger.info(`用户 ${secUid} 的作品数量: ${awemeCount}`);
      return awemeCount;
    }
    
    logger.warning(`无法从响应中提取作品数量: ${JSON.stringify(data)}`);
    return null;
  } catch (error) {
    logger.error(`获取用户作品数量失败: ${error.message}`, error);
    return null;
  }
}

// =============== 文件下载和发送 ===============

// 转换时间戳为北京时间格式
function formatToBeijingTime(timestamp) {
  if (!timestamp) return '未知';
  
  let date;
  
  try {
    // 处理不同的时间格式
    if (typeof timestamp === 'string') {
      // PostgreSQL timestamp格式 (如 "2024-12-27 06:32:00+00")
      // ISO字符串格式 (如 "2024-12-27T06:32:00.000Z")
      // 或其他日期字符串
      if (timestamp.includes('T') || timestamp.includes('-') || timestamp.includes(':')) {
        date = new Date(timestamp);
      } else {
        // 字符串格式的时间戳
        const ts = parseInt(timestamp);
        if (isNaN(ts)) {
          logger.warning(`[formatToBeijingTime] 无法解析的时间字符串: ${timestamp}`);
          return '未知';
        }
        // 如果是秒级时间戳（10位），转换为毫秒
        date = new Date(ts < 10000000000 ? ts * 1000 : ts);
      }
    } else if (typeof timestamp === 'number') {
      // 数字格式的时间戳
      // 如果是秒级时间戳（10位），转换为毫秒
      date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else {
      logger.warning(`[formatToBeijingTime] 未知的时间类型: ${typeof timestamp}, 值: ${timestamp}`);
      return '未知';
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      logger.warning(`[formatToBeijingTime] 无效的时间值: ${timestamp}`);
      return '未知';
    }
    
    // 检查年份是否合理（避免1970年的问题）
    if (date.getFullYear() < 2000) {
      logger.warning(`[formatToBeijingTime] 时间年份异常 (${date.getFullYear()}): ${timestamp}`);
      return '未知';
    }
    
    // 直接使用toLocaleString转换为北京时间
    const beijingTimeStr = date.toLocaleString('zh-CN', { 
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    return `${beijingTimeStr} (北京时间)`;
  } catch (error) {
    logger.error(`[formatToBeijingTime] 转换时间出错: ${error.message}, 原始值: ${timestamp}`);
    return '未知';
  }
}

function buildCaptionForSingle(parsedData) {
  const base = parsedData?.base_info || {};
  const author = parsedData?.author_info || {};
  const stats = parsedData?.statistics || {};
  const music = parsedData?.music_info || {};
  const location = parsedData?.location_info || {};

  const lines = [];
  if (base.aweme_id) {
    lines.push(`作品ID: ${base.aweme_id}`);
  }
  if (base.desc) {
    lines.push(`描述: ${base.desc}`);
  }
  // 只有当create_time有有效值时才显示发布时间
  if (base.create_time && base.create_time !== null && base.create_time !== '') {
    const formattedTime = formatToBeijingTime(base.create_time);
    // 如果转换后的时间不是"未知"，才添加到caption
    if (formattedTime !== '未知') {
      lines.push(`发布时间: ${formattedTime}`);
    }
  }
  if (author.nickname) {
    lines.push(`作者昵称: ${author.nickname}`);
  }
  if (author.unique_id) {
    lines.push(`抖音号: ${author.unique_id}`);
  }
  if (author.uid) {
    lines.push(`作者UID: ${author.uid}`);
  }

  const fc = author.follower_count;
  const tf = author.total_favorited;
  if (fc !== undefined && tf !== undefined) {
    lines.push(`粉丝数: ${fc} | 获赞: ${tf}`);
  }

  const province = location.province || "";
  const city = location.city || "";
  if (province || city) {
    lines.push(`地点: ${province} ${city}`.trim());
  }

  const digg = stats.digg_count;
  const cmt = stats.comment_count;
  const shr = stats.share_count;
  const col = stats.collect_count;
  const statsParts = [];
  
  if (digg !== undefined) {
    statsParts.push(`点赞: ${digg}`);
  }
  if (cmt !== undefined) {
    statsParts.push(`评论: ${cmt}`);
  }
  if (shr !== undefined) {
    statsParts.push(`分享: ${shr}`);
  }
  if (col !== undefined) {
    statsParts.push(`收藏: ${col}`);
  }
  
  if (statsParts.length > 0) {
    lines.push(statsParts.join(" | "));
  }

  if (music.title && music.author) {
    lines.push(`音乐: ${music.title} - ${music.author}`);
  }

  return lines.join("\n").trim();
}


function chunkList(array, size) {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// 智能按钮排版函数
function smartButtonLayout(buttons) {
  const count = buttons.length;
  
  // 1个按钮：单独一行
  if (count === 1) {
    return [buttons];
  }
  
  // 2-4个按钮：一行显示
  if (count <= 4) {
    return [buttons];
  }
  
  // 5个按钮：3+2
  if (count === 5) {
    return [
      buttons.slice(0, 3),
      buttons.slice(3, 5)
    ];
  }
  
  // 6个按钮：3+3
  if (count === 6) {
    return [
      buttons.slice(0, 3),
      buttons.slice(3, 6)
    ];
  }
  
  // 7个按钮：4+3
  if (count === 7) {
    return [
      buttons.slice(0, 4),
      buttons.slice(4, 7)
    ];
  }
  
  // 8个按钮：4+4
  if (count === 8) {
    return [
      buttons.slice(0, 4),
      buttons.slice(4, 8)
    ];
  }
  
  // 9个按钮：3+3+3
  if (count === 9) {
    return [
      buttons.slice(0, 3),
      buttons.slice(3, 6),
      buttons.slice(6, 9)
    ];
  }
  
  // 10个按钮：4+3+3
  if (count === 10) {
    return [
      buttons.slice(0, 4),
      buttons.slice(4, 7),
      buttons.slice(7, 10)
    ];
  }
  
  // 11个按钮：4+4+3
  if (count === 11) {
    return [
      buttons.slice(0, 4),
      buttons.slice(4, 8),
      buttons.slice(8, 11)
    ];
  }
  
  // 12个按钮：4+4+4
  if (count === 12) {
    return [
      buttons.slice(0, 4),
      buttons.slice(4, 8),
      buttons.slice(8, 12)
    ];
  }
  
  // 超过12个按钮：尽量每行4个，最后一行至少2个
  const rows = [];
  let i = 0;
  
  while (i < count) {
    const remaining = count - i;
    
    // 如果剩余5个，分成3+2
    if (remaining === 5) {
      rows.push(buttons.slice(i, i + 3));
      rows.push(buttons.slice(i + 3, i + 5));
      break;
    }
    
    // 如果剩余的可以被4整除，或者剩余大于5个，使用4个一行
    if (remaining % 4 === 0 || remaining > 5) {
      rows.push(buttons.slice(i, i + 4));
      i += 4;
    } else {
      // 否则使用3个一行
      rows.push(buttons.slice(i, i + 3));
      i += 3;
    }
  }
  
  return rows;
}

async function getOrCreateUser(chatId, username = "") {
  try {
    const { data, error } = await supabase
      .from("users2")
      .select("*")
      .eq("user_id", chatId)
      .limit(1);
    
    if (error) throw error;
    
    if (data && data.length > 0) {
      const existingUser = data[0];
      let updates = {};
      
      if (username && existingUser.username !== username) {
        updates.username = username;
      }
      
      if (existingUser.douyin === null || existingUser.douyin === undefined || existingUser.douyin === '') {
        updates.douyin = "tier0";
        if (existingUser.today === null || existingUser.today === undefined) {
          updates.today = 20;
        }
        if (existingUser.already === null || existingUser.already === undefined) {
          updates.already = 0;
        }
      }
      
      if (Object.keys(updates).length > 0) {
        await supabase
          .from("users2")
          .update(updates)
          .eq("user_id", chatId);
        
        const { data: updatedData, error: updatedError } = await supabase
          .from("users2")
          .select("*")
          .eq("user_id", chatId)
          .limit(1);
          
        if (updatedError) throw updatedError;
        return updatedData?.[0] || existingUser; 
      }
      
      return existingUser;
    } else {
      const insertData = {
        user_id: chatId,
        username: username || null,
        douyin: "tier0",
        today: 20,
        already: 0
      };
      
      const { data: insertResult, error: insertError } = await supabase
        .from("users2")
        .insert([insertData])
        .select();
      
      if (insertError) throw insertError;
      return insertResult?.[0] || insertData;
    }
  } catch (error) {
    logger.error(`数据库查询/插入用户失败: ${error.message}`, error);
    return {
      user_id: chatId,
      username: username || null,
      douyin: "tier0",
      today: 20,
      already: 0
    };
  }
}

async function updateUser(chatId, updates) {
  try {
    await supabase
      .from("users2")
      .update(updates)
      .eq("user_id", chatId);
  } catch (error) {
    logger.error(`更新用户 ${chatId} 信息失败: ${error.message}`, error);
  }
}

function isPremiumUser(userRecord) {
  return userRecord && userRecord.douyin === "tier1";
}

// 检查用户是否已订阅某作者
async function isUserSubscribedToAuthor(chatId, uid) {
  try {
    if (!chatId || !uid) {
      return false;
    }
    
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅作者 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    
    return count > 0;
  } catch (error) {
    logger.error(`检查订阅状态异常: ${error.message}`, error);
    return false;
  }
}

// 订阅数量检查
async function checkSubscriptionLimit(chatId, ctx) {
  try {
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (error) {
      logger.error(`检查用户 ${chatId} 订阅数量失败: ${error.message}`, error);
      await ctx.telegram.sendMessage(chatId, "检查订阅数量失败，请稍后再试。");
      return false;
    }
    
    if (count >= 2) {
      const message = '您是普通用户，最多可以订阅2个作者。升级到Premium可获得更高配额，请联系 <a href="https://t.me/juaer">@juaer</a> 了解详情。';
      await ctx.telegram.sendMessage(chatId, message, { parse_mode: 'HTML' });
      return false;
    }
    
    return true;
  } catch (error) {
    logger.error(`检查订阅数量异常: ${error.message}`, error);
    await ctx.telegram.sendMessage(chatId, "检查订阅数量出错，请稍后再试。");
    return false;
  }
}

function detectFileType(fileId) {
  if (!fileId || typeof fileId !== 'string') return null;

  if (fileId.startsWith('AgACAg')) {
    return 'photo';
  } else if (fileId.startsWith('BAACAg')) {
    return 'video';
  } else if (fileId.startsWith('BQACAg') || fileId.startsWith('BQADAg') || fileId.startsWith('BQACAgU')) {
    return 'document';
  } else if (fileId.startsWith('CQADAg') || fileId.startsWith('CQACAgU')) {
    return 'audio';
  } else if (fileId.startsWith('AwADAg')) {
    return 'voice';
  } else if (fileId.startsWith('CAADAg')) {
    return 'sticker';
  } else if (fileId.startsWith('CgADAg')) {
    return 'animation';
  } else if (fileId.startsWith('DQADAg')) {
    return 'video_note';
  }
  return null;
}

/**
 * 使用备用 bot tokens 发送媒体到 channel 来激活 file_ids
 * @param {Array<string>} fileIds - 要发送的文件ID数组
 * @returns {Promise<Array<string>>} 成功激活的 file_ids 数组
 */
async function sendMediaToChannelWithBackupTokens(fileIds) {
  const activatedFileIds = [];
  
  for (const backupToken of BACKUP_BOT_TOKENS) {
    if (!backupToken) continue;
    
    try {
      const backupBot = new Telegraf(backupToken);
      
      for (const fileId of fileIds) {
        const fileType = detectFileType(fileId);
        let result;
        
        try {
          switch (fileType) {
            case 'photo':
              result = await backupBot.telegram.sendPhoto(STORAGE_CHANNEL_ID, fileId);
              break;
            case 'video':
              result = await backupBot.telegram.sendVideo(STORAGE_CHANNEL_ID, fileId);
              break;
            case 'document':
              result = await backupBot.telegram.sendDocument(STORAGE_CHANNEL_ID, fileId);
              break;
            case 'audio':
              result = await backupBot.telegram.sendAudio(STORAGE_CHANNEL_ID, fileId);
              break;
            case 'voice':
              result = await backupBot.telegram.sendVoice(STORAGE_CHANNEL_ID, fileId);
              break;
            case 'sticker':
              result = await backupBot.telegram.sendSticker(STORAGE_CHANNEL_ID, fileId);
              break;
            case 'animation':
              result = await backupBot.telegram.sendAnimation(STORAGE_CHANNEL_ID, fileId);
              break;
            default:
              // 尝试作为 document 发送
              result = await backupBot.telegram.sendDocument(STORAGE_CHANNEL_ID, fileId);
          }
          logger.info(`使用备用 token ${backupToken.substr(0, 10)}... 成功激活 file_id: ${fileId}`);
          activatedFileIds.push(fileId);
        } catch (error) {
          logger.error(`使用备用 token ${backupToken.substr(0, 10)}... 发送 file_id 失败: ${error.message}`);
          continue;
        }
      }
      
      // 如果至少有一个文件成功激活，就返回（不需要全部成功）
      if (activatedFileIds.length > 0) {
        return activatedFileIds;
      }
    } catch (error) {
      logger.error(`备用 token ${backupToken.substr(0, 10)}... 初始化失败: ${error.message}`);
      continue;
    }
  }
  
  return activatedFileIds;
}

async function sendMediaByFileIds(ctx, fileIds, caption, markup = null, awemeId = null, skipSentUpdate = false) {
  if (!fileIds || fileIds.length === 0) {
    return;
  }
  
  const fileIdArray = Array.isArray(fileIds) ? fileIds : fileIds.split(';').filter(id => id);
  if (fileIdArray.length === 0) {
    return;
  }
  
  const chatId = ctx.chat.id;
  
  try {
    const isStorageChannel = (chatId === STORAGE_CHANNEL_ID);
    if (isStorageChannel) {
      for (const fileId of fileIdArray) {
        const fileType = detectFileType(fileId);
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
        } else if (fileType === 'document') {
          await ctx.telegram.sendDocument(chatId, fileId);
        } else if (fileType === 'audio') {
          await ctx.telegram.sendAudio(chatId, fileId);
        } else if (fileType === 'voice') {
          await ctx.telegram.sendVoice(chatId, fileId);
        } else if (fileType === 'sticker') {
          await ctx.telegram.sendSticker(chatId, fileId);
        } else if (fileType === 'animation') {
          await ctx.telegram.sendAnimation(chatId, fileId);
        } else if (fileType === 'video_note') {
          await ctx.telegram.sendVideoNote(chatId, fileId);
        } else {
          await ctx.telegram.sendDocument(chatId, fileId);
        }
      }
      return;
    }
    
    const photoIds = [];
    const videoIds = [];
    const zipIds = [];
    const audioIds = [];
    const otherIds = [];
    
    for (const fileId of fileIdArray) {
      const fileType = detectFileType(fileId);
      if (fileType === 'photo') {
        photoIds.push(fileId);
      } else if (fileType === 'video') {
        videoIds.push(fileId);
      } else if (fileType === 'document') {
        zipIds.push(fileId);
      } else if (fileType === 'audio') {
        audioIds.push(fileId);
      } else {
        otherIds.push(fileId);
      }
    }
    
    const mediaIds = [...photoIds, ...videoIds];
    let mediaSent = false;
    for (let i = 0; i < mediaIds.length; i += 10) {
      const chunk = mediaIds.slice(i, i + 10);
      
      if (chunk.length === 1) {
        const fileId = chunk[0];
        const fileType = detectFileType(fileId);
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
          mediaSent = true;
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
          mediaSent = true;
        }
      } else if (chunk.length > 1) {
        const mediaGroup = chunk.map(fileId => {
          const fileType = detectFileType(fileId);
          return {
            type: fileType === 'photo' ? 'photo' : 'video',
            media: fileId
          };
        });
        await ctx.telegram.sendMediaGroup(chatId, mediaGroup);
        mediaSent = true;
      }
    }
    
    if (caption || mediaSent) {
      if (caption) {
        await ctx.telegram.sendMessage(chatId, caption, {
          parse_mode: 'HTML',
          ...(markup ? markup : {})
        });
      } else if (markup) {
        await ctx.telegram.sendMessage(chatId, "_", {
          parse_mode: 'HTML',
          ...(markup ? markup : {}),
          disable_notification: true 
        });
      }
    }
    
    // 发送成功，只有在批量发送（高危操作）时才更新 sent 字段
    if (awemeId && !skipSentUpdate) {
      try {
        const { error: updateError } = await supabase
          .from('douyin')
          .update({ sent: true })
          .eq('aweme_id', awemeId);
        
        if (updateError) {
          logger.error(`更新 douyin 表 sent 字段失败: ${updateError.message}`, updateError);
        } else {
          logger.info(`已更新 douyin 表，标记作品 ${awemeId} 为已发送`);
        }
      } catch (error) {
        logger.error(`更新 douyin 表时发生异常: ${error.message}`, error);
      }
    }
    
  } catch (error) {
    const isWrongFileIdError = error.response 
      && error.response.error_code === 400 
      && error.response.description 
      && error.response.description.includes('wrong file identifier');
    
    if (isWrongFileIdError) {
      logger.error(`通过file_id发送媒体失败 (awemeId: ${awemeId}) - Wrong File Identifier: ${error.message}`);
      
      // 尝试使用备用 tokens 发送到 channel 来激活 file_id
      logger.info(`尝试使用备用 bot tokens 发送媒体到 channel 来激活 file_id...`);
      const activationSuccess = await sendMediaToChannelWithBackupTokens(fileIdArray);
      
      if (activationSuccess.length > 0) {
        logger.info(`成功使用备用 tokens 激活了 ${activationSuccess.length} 个 file_id`);
        
        // 使用原来的 file_ids 重新发送给用户（现在应该可以工作了）
        try {
          await sendMediaByFileIds(ctx, fileIdArray, caption, markup, awemeId, skipSentUpdate);
          logger.info(`成功使用原始 file_ids 发送媒体给用户`);
          
          // 不再更新 file_id 字段，只在批量发送时更新 sent 状态
          if (awemeId && !skipSentUpdate) {
            try {
              const { error: updateError } = await supabase
                .from('douyin')
                .update({ 
                  sent: true 
                })
                .eq('aweme_id', awemeId);
              
              if (updateError) {
                logger.error(`更新 douyin 表 sent 字段失败: ${updateError.message}`, updateError);
              } else {
                logger.info(`已更新 douyin 表，标记作品 ${awemeId} 为已发送`);
              }
            } catch (error) {
              logger.error(`更新 douyin 表时发生异常: ${error.message}`, error);
            }
          }
          return;
        } catch (retryError) {
          logger.error(`使用新 file_ids 发送失败: ${retryError.message}`);
        }
      }
      
      // 如果备用方案也失败，只在批量发送时更新 sent 字段为 false
      if (awemeId && !skipSentUpdate) {
        try {
          const { error: updateError } = await supabase
            .from('douyin')
            .update({ sent: false })
            .eq('aweme_id', awemeId);
          
          if (updateError) {
            logger.error(`更新 douyin 表 sent 字段失败: ${updateError.message}`, updateError);
          } else {
            logger.info(`已更新 douyin 表，标记作品 ${awemeId} 发送失败`);
          }
        } catch (error) {
          logger.error(`更新 douyin 表时发生异常: ${error.message}`, error);
        }
      }
      
      // 将作品标记为 large，交给后台处理
      await updateLargeField(ctx, awemeId, "file_id发送失败，需要重新处理");
      // 内部日志：文件ID已失效，正在后台重新处理
      return; 
    }
    
    logger.error(`通过file_id发送媒体失败 (awemeId: ${awemeId}): ${error.message}`, error);
    try {
      let sentAny = false;
      for (const fileId of fileIdArray) {
        const fileType = detectFileType(fileId);
        if (fileType === 'photo') {
          await ctx.telegram.sendPhoto(chatId, fileId);
          sentAny = true;
        } else if (fileType === 'video') {
          await ctx.telegram.sendVideo(chatId, fileId);
          sentAny = true;
        }
      }
      if (caption || sentAny) {
        if (caption) {
          await ctx.telegram.sendMessage(chatId, caption, {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        } else if (markup) {
          await ctx.telegram.sendMessage(chatId, "_", {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        }
      }
    } catch (fallbackError) {
      const isFallbackWrongFileIdError = fallbackError.response 
        && fallbackError.response.error_code === 400 
        && fallbackError.response.description 
        && fallbackError.response.description.includes('wrong file identifier');
      
      if (isFallbackWrongFileIdError) {
        logger.error(`单个发送也失败 (awemeId: ${awemeId}) - Wrong File Identifier: ${fallbackError.message}`);
        
        // 尝试使用备用 tokens 发送到 channel 来激活 file_id
        logger.info(`fallback也失败，尝试使用备用 bot tokens 发送媒体到 channel 来激活 file_id...`);
        const activationSuccess = await sendMediaToChannelWithBackupTokens(fileIdArray);
        
        if (activationSuccess.length > 0) {
          logger.info(`成功使用备用 tokens 激活了 ${activationSuccess.length} 个 file_id`);
          
          // 使用原来的 file_ids 重新发送给用户（现在应该可以工作了）
          try {
            await sendMediaByFileIds(ctx, fileIdArray, caption, markup, awemeId, skipSentUpdate);
            logger.info(`成功使用原始 file_ids 发送媒体给用户`);
            
            // 不再更新 file_id 字段，只在批量发送时更新 sent 状态
            if (awemeId && !skipSentUpdate) {
              try {
                const { error: updateError } = await supabase
                  .from('douyin')
                  .update({ 
                    sent: true 
                  })
                  .eq('aweme_id', awemeId);
                
                if (updateError) {
                  logger.error(`更新 douyin 表 sent 字段失败: ${updateError.message}`, updateError);
                } else {
                  logger.info(`已更新 douyin 表，标记作品 ${awemeId} 为已发送`);
                }
              } catch (error) {
                logger.error(`更新 douyin 表时发生异常: ${error.message}`, error);
              }
            }
            return;
          } catch (retryError) {
            logger.error(`使用新 file_ids 发送失败: ${retryError.message}`);
          }
        }
        
        // 如果备用方案也失败，只在批量发送时更新 sent 字段为 false
        if (awemeId && !skipSentUpdate) {
          try {
            const { error: updateError } = await supabase
              .from('douyin')
              .update({ sent: false })
              .eq('aweme_id', awemeId);
            
            if (updateError) {
              logger.error(`更新 douyin 表 sent 字段失败: ${updateError.message}`, updateError);
            } else {
              logger.info(`已更新 douyin 表，标记作品 ${awemeId} 发送失败`);
            }
          } catch (error) {
            logger.error(`更新 douyin 表时发生异常: ${error.message}`, error);
          }
        }
        
        await markWorkAsFailed(awemeId, ctx.chat.id);
        return;
      }
      
      logger.error(`单个发送也失败 (awemeId: ${awemeId}): ${fallbackError.message}`, fallbackError);
      try {
        if (caption) {
          await ctx.telegram.sendMessage(chatId, caption, {
            parse_mode: 'HTML',
            ...(markup ? markup : {})
          });
        }
      } catch (finalError) {
        logger.error(`发送说明文本也失败: ${finalError.message}`, finalError);
      }
    }
  }
}


async function updateLargeField(ctx, awemeId, reason) {
  const chatId = ctx.chat?.id;
  if (!chatId) {
    logger.warning(`无法更新 large 字段，因为缺少 chatId (awemeId: ${awemeId})`);
    return;
  }
  
  try {
    const { data: currentData, error: queryError } = await supabase
      .from("douyin")
      .select("large")
      .eq("aweme_id", awemeId)
      .single();
    
    if (queryError && queryError.code !== 'PGRST116') {
      logger.error(`查询作品 ${awemeId} 的large字段失败: ${queryError.message}`, queryError);
    }
    
    let newLargeValue = null;
    const currentChatIdStr = String(chatId);
    const currentValue = currentData?.large;
    if (currentValue) {
      const chatIds = currentValue.split(';').filter(id => id);
      if (!chatIds.includes(currentChatIdStr)) {
        chatIds.push(currentChatIdStr);
        newLargeValue = chatIds.join(';');
      } else {
        newLargeValue = currentValue;
      }
    } else {
      newLargeValue = currentChatIdStr;
    }
    
    const { error: updateError } = await supabase
      .from("douyin")
      .update({ large: newLargeValue })
      .eq("aweme_id", awemeId);
      
    if (updateError) {
      logger.error(`更新作品 ${awemeId} 的large字段失败 (${reason}): ${updateError.message}`, updateError);
      throw updateError;
    } else {
      logger.info(`已将作品 ${awemeId} 的large字段更新为 ${newLargeValue} (原因: ${reason})`);
      // 不再发送大文件通知消息
      // if (ctx.reply) {
      //   try {
      //     await ctx.reply("文件过大，后台处理中，请稍后再查看。");
      //   } catch (replyError) {
      //     logger.error(`发送大文件通知失败: ${replyError.message}`, replyError);
      //   }
      // }
    }
  } catch (error) {
    logger.error(`执行 updateLargeField 时发生未预料错误 (awemeId: ${awemeId}): ${error.message}`, error);
  }
}

// =============== 通用重试函数 ===============
async function retryFailedSends(aweme_id, targetChatIds, bot) {
  try {
    logger.info(`[Retry Send Start] 开始为作品 ${aweme_id} 重试发送给 ${targetChatIds.length} 个用户: ${targetChatIds.join(', ')}`);
    
    // 获取作品完整信息
    const { data: workData, error: workError } = await supabase
      .from("douyin")
      .select("*")
      .eq("aweme_id", aweme_id)
      .single();
    
    if (workError || !workData) {
      logger.error(`[Retry Send Failed] 获取作品 ${aweme_id} 信息失败: ${workError?.message || "无数据"}`);
      return;
    }
    
    const file_id = workData.file_id;
    if (!file_id) {
      logger.warning(`[Retry Send Failed] 作品 ${aweme_id} 的 file_id 为空，无法重试发送`);
      return;
    }
    
    logger.info(`[Retry Send] 作品 ${aweme_id} file_id: ${file_id.substring(0, 50)}...`)
    
    // 构建caption
    const caption = buildCaptionForSingle({
      base_info: { 
        aweme_id: workData.aweme_id, 
        desc: workData.description, 
        create_time: workData.create_time 
      },
      author_info: { 
        nickname: workData.nickname, 
        uid: workData.uid, 
        sec_uid: workData.sec_uid, 
        unique_id: workData.unique_id, 
        follower_count: workData.follower_count, 
        total_favorited: workData.total_favorited 
      },
      location_info: { 
        province: workData.province, 
        city: workData.city 
      },
      music_info: { 
        title: workData.music_title, 
        author: workData.music_author, 
        play_url: workData.music_play_url 
      }
    });
    
    // 处理文件IDs
    const fileIdsToSend = file_id.split(';').filter(id => id);
    let zipId = null;
    let audioFileId = null;
    
    for (const id of fileIdsToSend) {
      if (detectFileType(id) === 'document') zipId = id;
      if (detectFileType(id) === 'audio') audioFileId = id;
    }
    
    let successCount = 0;
    let failedCount = 0;
    
    // 向每个目标用户发送
    for (const chatId of targetChatIds) {
      if (chatId === STORAGE_CHANNEL_ID) continue;
      
      try {
        // 构建按钮
        const buttons = [];
        buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${workData.aweme_id}`));
        buttons.push(Markup.button.callback("获取音乐", `music:${workData.aweme_id}`));
        if (zipId) buttons.push(Markup.button.callback("获取文件", `zip:${workData.aweme_id}`));
        
        // 检查订阅状态
        if (workData.uid && chatId) {
          const isSubscribedWork = await isUserSubscribedWork(chatId, workData.uid);
          const isSubscribedLive = await isUserSubscribedLive(chatId, workData.uid);
          
          if (isSubscribedWork) {
            buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${workData.uid}:${workData.aweme_id}`));
          } else {
            buttons.push(Markup.button.callback("订阅作品", `sub:${workData.uid}`));
          }
          
          if (SHOW_LIVE_BUTTONS) {
            if (isSubscribedLive) {
              buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${workData.uid}`));
            } else {
              buttons.push(Markup.button.callback("订阅直播", `sub_live:${workData.uid}`));
            }
          }
        }
        
        const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));
        
        // 发送媒体
        await sendMediaByFileIds(
          { 
            telegram: bot.telegram, 
            chat: { id: chatId }, 
            reply: (text, extra) => bot.telegram.sendMessage(chatId, text, extra) 
          },
          fileIdsToSend.filter(id => id !== zipId),
          caption,
          markup,
          aweme_id
        );
        
        logger.info(`[Retry Send Success] 成功向用户 ${chatId} 重试发送作品 ${aweme_id}`);
        successCount++;
        
      } catch (error) {
        logger.error(`[Retry Send Failed] 向用户 ${chatId} 重试发送作品 ${aweme_id} 失败: ${error.message}`);
        failedCount++;
        
        // 如果仍然是file ID错误，需要记录
        if (error.response?.error_code === 400 && 
            error.response?.description?.includes('wrong file identifier')) {
          logger.error(`[Retry Send Error] 持续的 file_id 错误，将更新数据库记录`);
          // 更新failed字段，记录持续失败的情况
          await supabase
            .from("douyin")
            .update({ failed: `persistent file id error for chat ${chatId}` })
            .eq("aweme_id", aweme_id);
        }
      }
    }
    
    logger.info(`[Retry Send Summary] 作品 ${aweme_id} 重试完成 - 成功: ${successCount}/${targetChatIds.length}, 失败: ${failedCount}/${targetChatIds.length}`);
    
  } catch (error) {
    logger.error(`执行 retryFailedSends 时发生错误: ${error.message}`, error);
  }
}

// =============== 实时监听 ===============
async function initRealtimeListener(bot) {
  try {
    logger.info("正在初始化Realtime监听...");

    const channel = supabase
      .channel('douyin_all_updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'douyin',
        },
        async (payload) => {
          try {
            const oldData = payload.old;
            const newData = payload.new;
            const aweme_id = newData?.aweme_id;
            
            if (!aweme_id) {
              logger.warning(`检测到更新事件，但缺少 aweme_id`, payload);
              return;
            }

            // 检查 douyin 表中的 sent 字段
            const { data: douyinData, error: douyinError } = await supabase
              .from('douyin')
              .select('sent')
              .eq('aweme_id', aweme_id)
              .single();
            
            if (douyinError) {
              logger.error(`查询 douyin 表出错: ${douyinError.message}`, douyinError);
              // 如果查询出错，继续处理以保持兼容性
            } else if (douyinData?.sent === true) {
              logger.info(`[Realtime Update Ignored] aweme_id: ${aweme_id}. 该作品已发送过 (sent=true)，忽略此更新`);
              return;
            }

            const oldFailed = oldData?.failed;
            const newFailed = newData?.failed;
            const oldLarge = oldData?.large;
            const newLarge = newData?.large;
            const oldFileId = oldData?.file_id;
            const newFileId = newData?.file_id;
            
            const fileIdChanged = newData?.file_id && oldData?.file_id !== newData.file_id;
            const failedChanged = oldFailed !== newFailed;
            const largeChanged = oldLarge !== newLarge;
            
            // 移除去重机制相关代码，因为现在从源头解决了问题
            
            // 如果是large字段从有内容变为null/空值，这是后台处理完成的清理动作，不应该触发发送
            if (oldLarge && !newLarge) {
              logger.info(`[Realtime Update Ignored] aweme_id: ${aweme_id}. large字段清理操作，不触发发送`);
              return;
            }
            
            logger.info(`[Realtime Update] aweme_id: ${aweme_id}, fileIdChanged: ${fileIdChanged}, failed: ${oldFailed} -> ${newFailed}, large: ${oldLarge} -> ${newLarge}`);

            // 处理failed字段被清除的情况（错误被修正）
            if (oldFailed && !newFailed && newData?.file_id) {
              logger.info(`[Realtime Condition Met] failed字段被清除 for ${aweme_id}. 检查是否需要重新发送.`);
              logger.info(`[Failed Clear Detail] aweme_id: ${aweme_id}, oldFailed: "${oldFailed}", file_id: ${newData.file_id ? "存在" : "不存在"}`);
              
              // 解析之前的错误信息，获取需要重试的chat ID
              let targetChatIds = [];
              
              // 处理 "file id pending:xxx" 格式
              const pendingMatch = oldFailed.match(/^file id pending:(\d+)$/);
              if (pendingMatch) {
                targetChatIds.push(parseInt(pendingMatch[1]));
                logger.info(`[Failed Parse] 从 "file id pending" 格式解析出 chatId: ${pendingMatch[1]}`);
              }
              
              // 处理其他可能的错误格式（如直接包含chat ID的错误）
              const chatIdMatch = oldFailed.match(/chat[_\s]?id[:\s]+(\d+)/i);
              if (chatIdMatch) {
                targetChatIds.push(parseInt(chatIdMatch[1]));
                logger.info(`[Failed Parse] 从其他格式解析出 chatId: ${chatIdMatch[1]}`);
              }
              
              if (targetChatIds.length > 0) {
                logger.info(`[Retry Decision] 将重新发送作品 ${aweme_id} 给 ${targetChatIds.length} 个用户: ${targetChatIds.join(', ')}`);
                await retryFailedSends(aweme_id, targetChatIds, bot);
              } else {
                logger.info(`[Retry Decision] 无法从 oldFailed "${oldFailed}" 中解析出需要重试的用户，不进行重新发送`);
              }
            }
            
            // 处理large字段被清除的情况（大文件问题被解决）
            // 注意：这个逻辑是为了处理后台程序处理完大文件后的情况
            // 但如果是file_id刚刚变化触发的清除，就不应该再次发送
            // 暂时注释掉这个逻辑，避免重复发送
            /*
            if (oldLarge && !newLarge && newData?.file_id) {
              logger.info(`[Realtime Condition Met] large字段被清除 for ${aweme_id}. 准备重新发送给之前标记的用户.`);
              
              // 解析large字段中的chat IDs
              const targetChatIds = oldLarge.split(';')
                .map(id => id.trim())
                .filter(id => id)
                .map(id => parseInt(id))
                .filter(id => !isNaN(id));
              
              if (targetChatIds.length > 0) {
                logger.info(`从旧的large字段解析出需要发送的用户: ${targetChatIds.join(', ')}`);
                await retryFailedSends(aweme_id, targetChatIds, bot);
              }
            }
            */

            const pendingMatch = oldFailed?.match(/^file id pending:(\d+)$/);
            if (pendingMatch && newFailed === "file id done") {
              const targetChatId = parseInt(pendingMatch[1]);
              if (!targetChatId) {
                logger.error(`[Realtime Resend Error] 无法从 oldFailed ("${oldFailed}") 中解析有效的 chatId for ${aweme_id}`);
                return;
              }
              logger.info(`[Realtime Condition Met] failed: pending:${targetChatId} -> done for ${aweme_id}. Triggering resend to ${targetChatId}.`);
              
              const { data: workFullData, error: workError } = await supabase
                .from("douyin")
                .select("*")
                .eq("aweme_id", aweme_id)
                .limit(1);

              if (workError || !workFullData || workFullData.length === 0) {
                logger.error(`重新发送：获取作品 ${aweme_id} 的完整信息失败: ${workError?.message || "无数据"}`, workError);
                return;
              }
              
              const work = workFullData[0];
              const file_id = work.file_id;
              if (!file_id) {
                logger.warning(`重新发送：作品 ${aweme_id} 的 file_id 为空，无法重新发送`);
                return;
              }
              
              const caption = buildCaptionForSingle({
                base_info: { aweme_id: work.aweme_id, desc: work.description, create_time: work.create_time },
                author_info: { nickname: work.nickname, uid: work.uid, sec_uid: work.sec_uid, unique_id: work.unique_id, follower_count: work.follower_count, total_favorited: work.total_favorited },
                location_info: { province: work.province, city: work.city },
                music_info: { title: work.music_title, author: work.music_author, play_url: work.music_play_url }
              });
              const fileIdsToSend = file_id.split(';').filter(id => id);
              let zipId = null;
              let audioFileId = null;
              for (const id of fileIdsToSend) {
                if (detectFileType(id) === 'document') zipId = id;
                if (detectFileType(id) === 'audio') audioFileId = id;
              }
              // 使用智能按钮排版函数，不再需要 chunkArray

              let resendSuccess = true;
              const targetChatIds = [targetChatId];
              
              for (const chatId of targetChatIds) {
                if (chatId === STORAGE_CHANNEL_ID) continue;
                try {
                  const buttons = [];
                  buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${work.aweme_id}`));
                  buttons.push(Markup.button.callback("获取音乐", `music:${work.aweme_id}`));
                  if (zipId) buttons.push(Markup.button.callback("获取文件", `zip:${work.aweme_id}`));
                  
                  // 检查用户是否已订阅该作者的作品和直播
                  let isSubscribedWork = false;
                  let isSubscribedLive = false;
                  try {
                    if (work.uid && chatId) {
                      isSubscribedWork = await isUserSubscribedWork(chatId, work.uid);
                      isSubscribedLive = await isUserSubscribedLive(chatId, work.uid);
                    }
                  } catch (error) {
                    logger.error(`检查订阅状态出错: ${error.message}`, error);
                  }
                  
                  // 根据作品/直播订阅状态添加按钮
                  if (work.uid) {
                    if (isSubscribedWork) {
                      buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${work.uid}:${work.aweme_id}`));
                    } else {
                      buttons.push(Markup.button.callback("订阅作品", `sub:${work.uid}`));
                    }

                    // 只有在启用了直播功能时才显示直播订阅按钮
                    if (SHOW_LIVE_BUTTONS) {
                      if (isSubscribedLive) {
                        buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${work.uid}`));
                      } else {
                        buttons.push(Markup.button.callback("订阅直播", `sub_live:${work.uid}`));
                      }
                    }
                  }
                  
                  const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));
                  
                  await sendMediaByFileIds(
                    { 
                      telegram: bot.telegram, 
                      chat: { id: chatId }, 
                      reply: (text, extra) => bot.telegram.sendMessage(chatId, text, extra) 
                    },
                    fileIdsToSend.filter(id => id !== zipId),
                    caption,
                    markup,
                    aweme_id,
                    true  // skipSentUpdate = true 低危操作（large字段指定用户）
                  );
                  logger.info(`已向用户 ${chatId} 重新发送作品 ${aweme_id} 的更新`);
                } catch (userError) {
                  logger.error(`向用户 ${chatId} 重新发送作品 ${aweme_id} 失败: ${userError.message}`, userError);
                  const isWrongFileIdError = userError.response
                    && userError.response.error_code === 400
                    && userError.response.description
                    && userError.response.description.includes('wrong file identifier');
                  if (isWrongFileIdError) {
                    logger.warning(`重新发送作品 ${aweme_id} 给用户 ${chatId} 再次遇到 400 错误，将保持 failed 状态`);
                    resendSuccess = false;
                  } else {
                    resendSuccess = false;
                  }
                }
              }
              
              if (resendSuccess) {
                logger.info(`尝试清理作品 ${aweme_id} 的 failed 字段`);
                try {
                  const { error: updateError } = await supabase
                    .from("douyin")
                    .update({ failed: null })
                    .eq("aweme_id", aweme_id);
                  if (updateError) {
                    logger.error(`清理作品 ${aweme_id} 的failed字段失败: ${updateError.message}`, updateError);
                  } else {
                    logger.info(`成功清理作品 ${aweme_id} 的failed字段`);
                  }
                } catch (clearError) {
                  logger.error(`清理作品 ${aweme_id} 的failed字段时发生异常: ${clearError.message}`, clearError);
                }
              } else {
                logger.info(`重新发送作品 ${aweme_id} 未完全成功或再次遇到错误，保持 failed 状态为 'file id done'`);
              }
            }
            else if (fileIdChanged 
                  && !(newFailed?.startsWith("file id pending:")) 
                  && newFailed !== "file id done") {
              
              // 检查是否file_id和failed同时变化（任务失败情况）
              // 注意：任务修正（failed从错误变为null）的重新发送由上面的专门逻辑处理
              // 这里只阻止任务失败时的首次错误发送
              const isOldFailedNormal = !oldFailed || oldFailed === "" || oldFailed === null;
              const isNewFailedError = newFailed && newFailed !== "" && newFailed !== "file id done";
              const failedFromNormalToError = isOldFailedNormal && isNewFailedError;
              
              if (failedFromNormalToError) {
                logger.info(`[Realtime Update Ignored] aweme_id: ${aweme_id}. file_id和failed同时变化（${oldFailed || 'null'} -> ${newFailed}），任务失败，不发送给用户`);
                return;
              }
              
              const file_id = newData.file_id;
              logger.info(`[Realtime Condition Met] file_id changed for ${aweme_id} (failed is '${newFailed}'). Triggering standard update.`);

              const { data: workFullData, error: workError } = await supabase
                .from("douyin")
                .select("*")
                .eq("aweme_id", aweme_id)
                .limit(1);

              if (workError || !workFullData || workFullData.length === 0) {
                logger.error(`获取作品 ${aweme_id} 的完整信息失败: ${workError?.message || "无数据"}`, workError);
                return;
              }

              const work = workFullData[0];
              const uid = work.uid;
              const actualLargeString = work.large; 
              
              // 确保作者信息存在于douyin_user表中
              if (work.uid && work.sec_uid) {
                const userData = {
                  uid: work.uid,
                  sec_uid: work.sec_uid,
                  nickname: work.nickname || null,
                  unique_id: work.unique_id || null,
                  follower_count: work.follower_count || 0,
                  total_favorited: work.total_favorited || 0
                };
                await saveDouyinUserToDatabase(userData);
              }
              
              let targetChatIds = [];
              let sendToAllSubscribers = true;
              let clearLargeField = false;
  
              if (actualLargeString) {
                logger.info(`作品 ${aweme_id} large字段有内容: "${actualLargeString}", 将仅发送给这些用户。`);
                targetChatIds = actualLargeString.split(';')
                  .map(id => id.trim()).filter(id => id)
                  .map(id => parseInt(id)).filter(id => !isNaN(id));
  
                if (targetChatIds.length > 0) {
                  sendToAllSubscribers = false;
                  clearLargeField = true;
                  logger.info(`作品 ${aweme_id} 将发送给 ${targetChatIds.length} 个指定用户: ${targetChatIds.join(', ')}`);
                } else {
                  logger.warning(`作品 ${aweme_id} large字段 "${actualLargeString}" 解析后为空或无效, 将按正常订阅逻辑发送。`);
                  sendToAllSubscribers = true;
                }
              } else {
                logger.info(`作品 ${aweme_id} large字段为空，将按正常订阅逻辑发送。`);
              }
              
              if (sendToAllSubscribers) {
                if (!uid) {
                  logger.warning(`作品 ${aweme_id} large字段为空，但缺少uid，无法查找订阅者`);
                  return;
                }
                
                const { data: userData, error: userError } = await supabase
                  .from("douyin_user")
                  .select("once, nickname")
                  .eq("uid", uid)
                  .limit(1);
                
                if (userError) {
                  logger.error(`查询作者 ${uid} 的once状态失败: ${userError.message}`, userError);
                  return;
                }
                if (!userData || userData.length === 0 || !userData[0].once) {
                  const nickname = userData?.[0]?.nickname || "未知用户";
                  logger.info(`作者 ${nickname} (${uid}) 的once字段不为true，不发送通知`);
                  return;
                }
                
                const nickname = userData[0].nickname || "未知用户";
                logger.info(`作者 ${nickname} (${uid}) 的once字段为true，继续检查时间限制`);
                
                // 检查作品发布时间是否超过2天
                if (work.create_time) {
                  const createTime = new Date(work.create_time);
                  const now = new Date();
                  const diffInMs = now - createTime;
                  const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
                  
                  if (diffInDays > 2) {
                    logger.info(`作品 ${aweme_id} 发布于 ${work.create_time}，距今已超过2天(${diffInDays.toFixed(1)}天)，不推送给订阅用户`);
                    return;
                  }
                  logger.info(`作品 ${aweme_id} 发布于 ${work.create_time}，距今${diffInDays.toFixed(1)}天，符合时间要求`);
                } else {
                  logger.warning(`作品 ${aweme_id} 缺少create_time字段，无法判断发布时间`);
                }
                
                logger.info(`作者 ${nickname} (${uid}) 开始查找订阅用户`);
                const { data: subscribers, error: subsError } = await supabase
                  .from("telegram_douyin_map")
                  .select("user_id")
                  .eq("uid", uid);
                
                if (subsError) {
                  logger.error(`获取作者 ${uid} 的订阅用户失败: ${subsError.message}`, subsError);
                  return;
                }
                if (!subscribers || subscribers.length === 0) {
                  logger.info(`作者 ${nickname} (${uid}) 没有订阅用户，不处理`);
                  return;
                }
                targetChatIds = subscribers.map(s => s.user_id);
                logger.info(`作者 ${nickname} (${uid}) 有 ${targetChatIds.length} 个订阅用户，开始发送更新...`);
              }
              
              const caption = buildCaptionForSingle({
                base_info: { aweme_id: work.aweme_id, desc: work.description, create_time: work.create_time },
                author_info: { nickname: work.nickname, uid: work.uid, sec_uid: work.sec_uid, unique_id: work.unique_id, follower_count: work.follower_count, total_favorited: work.total_favorited },
                location_info: { province: work.province, city: work.city },
                music_info: { title: work.music_title, author: work.music_author, play_url: work.music_play_url }
              });
              const fileIdsToSend = work.file_id ? work.file_id.split(';').filter(id => id) : [];
              if (fileIdsToSend.length === 0) {
                logger.warning(`作品 ${aweme_id} 的file_id为空或无效，不发送`);
                return;
              }
              
              let zipId = null;
              let audioFileId = null;
              for (const id of fileIdsToSend) {
                if (detectFileType(id) === 'document') zipId = id;
                if (detectFileType(id) === 'audio') audioFileId = id;
              }
              
              // 使用智能按钮排版函数，不再需要 chunkArray
              
              for (const chatId of targetChatIds) {
                if (chatId === STORAGE_CHANNEL_ID) {
                  logger.info(`跳过向 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 发送作品 ${aweme_id}`);
                  continue;
                }
                try {
                  const buttons = [];
                  buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${work.aweme_id}`));
                  buttons.push(Markup.button.callback("获取音乐", `music:${work.aweme_id}`));
                  if (zipId) buttons.push(Markup.button.callback("获取文件", `zip:${work.aweme_id}`));
                  
                  // 检查用户是否已订阅该作者的作品和直播
                  let isSubscribedWork = false;
                  let isSubscribedLive = false;
                  try {
                    if (work.uid && chatId) {
                      isSubscribedWork = await isUserSubscribedWork(chatId, work.uid);
                      isSubscribedLive = await isUserSubscribedLive(chatId, work.uid);
                    }
                  } catch (error) {
                    logger.error(`Realtime resend: 检查订阅状态出错 (uid: ${work.uid}): ${error.message}`, error);
                  }
                  
                  // 根据作品/直播订阅状态添加按钮
                  if (work.uid) {
                    if (isSubscribedWork) {
                      buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${work.uid}:${work.aweme_id}`));
                    } else {
                      buttons.push(Markup.button.callback("订阅作品", `sub:${work.uid}`));
                    }

                    // 只有在启用了直播功能时才显示直播订阅按钮
                    if (SHOW_LIVE_BUTTONS) {
                      if (isSubscribedLive) {
                        buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${work.uid}`));
                      } else {
                        buttons.push(Markup.button.callback("订阅直播", `sub_live:${work.uid}`));
                      }
                    }
                  }
                  
                  const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));
                  
                  await sendMediaByFileIds(
                    { 
                      telegram: bot.telegram, 
                      chat: { id: chatId }, 
                      reply: (text, extra) => bot.telegram.sendMessage(chatId, text, extra) 
                    },
                    fileIdsToSend.filter(id => id !== zipId),
                    caption,
                    markup,
                    aweme_id,
                    !sendToAllSubscribers  // skipSentUpdate = true 当不是批量发送时（低危操作）
                  );
                  logger.info(`已向用户 ${chatId} 发送作品 ${aweme_id} 的更新`);
                } catch (userError) {
                  logger.error(`向用户 ${chatId} 发送作品 ${aweme_id} 失败: ${userError.message}`, userError);
                  const isWrongFileIdError = userError.response
                    && userError.response.error_code === 400
                    && userError.response.description
                    && userError.response.description.includes('wrong file identifier');
                  if (isWrongFileIdError) {
                      await markWorkAsFailed(aweme_id, chatId);
                  }
                }
              }
              
              if (clearLargeField) {
                logger.info(`尝试清理作品 ${aweme_id} 的 large 字段`);
                try {
                  // 直接清理large字段，不使用临时标记
                  const { error: updateError } = await supabase
                    .from("douyin")
                    .update({ large: null })
                    .eq("aweme_id", aweme_id);
                  if (updateError) {
                    logger.error(`清理作品 ${aweme_id} 的large字段失败: ${updateError.message}`, updateError);
                  } else {
                    logger.info(`成功清理作品 ${aweme_id} 的large字段`);
                  }
                } catch (clearError) {
                  logger.error(`清理作品 ${aweme_id} 的large字段时发生异常: ${clearError.message}`, clearError);
                }
              }
            } else {
              // 静默忽略无关的更新
              // logger.info(`[Realtime Update Ignored] aweme_id: ${aweme_id}. No relevant change detected.`);
            }
          } catch (error) {
            logger.error(`处理file_id变化事件出错: ${error.message}`, error);
          }
        }
      )
      .subscribe();

    logger.info("等待Realtime订阅确认...");
    // 等待一小段时间让订阅生效
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    logger.info("Realtime监听初始化成功");
    return channel;
  } catch (error) {
    logger.error(`初始化Realtime监听失败: ${error.message}`, error);
    return null;
  }
}

// =============== 创建 Bot 实例 ===============
const bot = new Telegraf(process.env.BUTHISBOT);
console.log("=== Bot instance created ===");

// 设置 bot 不自动启动轮询
bot.telegram.webhookReply = false;

// =============== 命令处理 ===============
bot.command('start', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  const chatId = ctx.chat.id;
  const username = ctx.from.username || "";
  const userRecord = await getOrCreateUser(chatId, username);

  const welcomeText = 
    `👋 你好！这是抖音下载Bot。\n` +
    `你的 Chat ID: <code>${chatId}</code>\n` +
    `当前数据库记录的用户名: @${userRecord.username || ''}`;
    
  await ctx.reply(welcomeText, { parse_mode: 'HTML' });
});

bot.command('help', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  const chatId = ctx.chat.id;
  const userRecord = await getOrCreateUser(chatId, ctx.from.username || "");
  
  const sendAny = userRecord?.send_any ?? true;
  if (!sendAny) {
    return;
  }

  const helpText = 
    "【功能说明】\n" +
    "- 获取音乐：并非所有作品都提供音乐获取。\n" +
    "- 获取文件：将该作品所有媒体文件打包，避免telegram二次压缩图片文件。\n" +
    "- 订阅功能：抖音作者删除作品是很常见的事，并且随着时间画质会逐渐变差。点击订阅后，程序将在后台值守被订阅作者公开的新作品，我们的数据库将尽最大可能及时保留下作品内容。\n\n" +
    "【命令列表】\n" +
    "- /start - 查看欢迎信息\n" +
    "- /help - 查看帮助信息\n" +
    "- /douyin - 查看作品订阅列表\n" +
    "- /live - 查看直播订阅列表";

  await ctx.reply(helpText);
});

// 查看当前订阅
bot.command('douyin', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  const chatId = ctx.chat.id;
  const username = ctx.from.username || null;
  
  try {
    const userRecord = await getOrCreateUser(chatId, username);
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("douyin", true);
    
    if (countError) {
      logger.error(`获取订阅数量失败: ${countError.message}`, countError);
      await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (count === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者的作品。\n\n可以通过点击视频下方的「订阅作品」按钮来订阅创作者的作品。");
      return;
    }
    
    const limit = 10;
    const page = 1;
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .eq("douyin", true)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      logger.error(`获取订阅列表失败: ${error.message}`, error);
      await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者的作品。\n\n可以通过点击视频下方的「订阅作品」按钮来订阅创作者的作品。");
      return;
    }
    
    const buttons = [];
    let message = `📋 <b>你的作品订阅列表 (${count} 个订阅)</b>`;
    if (count > limit) {
      message += ` - 显示 ${Math.min(limit, subscriptions.length)}/${count}\n`;
    }
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      message += "\n";
      
      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅作品: ${creator.nickname || uid}`, `unsub:${uid}`)
      ]);
    }
    buttons.push([
      Markup.button.callback("🗑️ 取消所有订阅", "unsub_all")
    ]);
    
    if (count > limit) {
      const pageButtons = [];
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `douyin_page:${page - 1}`));
      }
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `douyin_page:${page + 1}`));
      }
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    await ctx.reply(message, {
      parse_mode: 'HTML',
      ...markup
    });
  } catch (error) {
    logger.error(`处理订阅列表命令出错: ${error.message}`, error);
    await ctx.reply("获取订阅列表时发生错误，请稍后再试。");
  }
});

// 查看直播订阅
bot.command('live', async (ctx) => {
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) { return; }
  const chatId = ctx.chat.id;
  const username = ctx.from.username || null;
  
  try {
    const userRecord = await getOrCreateUser(chatId, username);
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("live", true);
    
    if (countError) {
      logger.error(`获取直播订阅数量失败: ${countError.message}`, countError);
      await ctx.reply("获取直播订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (count === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者的直播。\n\n可以通过点击视频下方的「订阅直播」按钮来订阅创作者的直播。");
      return;
    }
    
    const limit = 10;
    const page = 1;
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .eq("live", true)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      logger.error(`获取直播订阅列表失败: ${error.message}`, error);
      await ctx.reply("获取直播订阅列表时发生错误，请稍后再试。");
      return;
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      await ctx.reply("你当前没有订阅任何抖音创作者的直播。\n\n可以通过点击视频下方的「订阅直播」按钮来订阅创作者的直播。");
      return;
    }
    
    const buttons = [];
    let message = `📺 <b>你的直播订阅列表 (${count} 个订阅)</b>`;
    if (count > limit) {
      message += ` - 显示 ${Math.min(limit, subscriptions.length)}/${count}\n`;
    }
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      message += "\n";
      
      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅直播: ${creator.nickname || uid}`, `unsub_live:${uid}`)
      ]);
    }
    buttons.push([
      Markup.button.callback("🗑️ 取消所有直播订阅", "unsub_all_live")
    ]);
    
    if (count > limit) {
      const pageButtons = [];
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `live_page:${page - 1}`));
      }
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `live_page:${page + 1}`));
      }
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    await ctx.reply(message, {
      parse_mode: 'HTML',
      ...markup
    });
  } catch (error) {
    logger.error(`处理直播订阅列表命令出错: ${error.message}`, error);
    await ctx.reply("获取直播订阅列表时发生错误，请稍后再试。");
  }
});

bot.action(/^douyin_page:(\d+)$/, async (ctx) => {
  const chatId = ctx.chat.id;
  const page = parseInt(ctx.match[1]);
  
  if (isNaN(page) || page < 1) {
    await ctx.answerCbQuery("无效的页码");
    return;
  }
  
  try {
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("douyin", true);
    
    if (countError) {
      await ctx.answerCbQuery("获取订阅数量失败");
      return;
    }
    
    const limit = 10;
    if ((page - 1) * limit >= count) {
      await ctx.answerCbQuery("页码超出范围");
      return;
    }
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .eq("douyin", true)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      await ctx.answerCbQuery("获取订阅列表失败");
      return;
    }
    
    const buttons = [];
    let message = `📋 <b>你的作品订阅列表 (${count} 个订阅)</b>`;
    if (count > limit) {
      message += ` - 显示 ${(page - 1) * limit + 1}-${Math.min(page * limit, count)}/${count}\n`;
    }
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      message += "\n";

      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅作品: ${creator.nickname || uid}`, `unsub:${uid}`)
      ]);
    }
    buttons.push([
      Markup.button.callback("🗑️ 取消所有订阅", "unsub_all")
    ]);
    
    if (count > limit) {
      const pageButtons = [];
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `douyin_page:${page - 1}`));
      }
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `douyin_page:${page + 1}`));
      }
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理翻页请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

// 处理直播订阅分页
bot.action(/^live_page:(\d+)$/, async (ctx) => {
  const chatId = ctx.chat.id;
  const page = parseInt(ctx.match[1]);
  
  if (isNaN(page) || page < 1) {
    await ctx.answerCbQuery("无效的页码");
    return;
  }
  
  try {
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("live", true);
    
    if (countError) {
      await ctx.answerCbQuery("获取直播订阅数量失败");
      return;
    }
    
    const limit = 10;
    if ((page - 1) * limit >= count) {
      await ctx.answerCbQuery("页码超出范围");
      return;
    }
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .eq("live", true)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) {
      await ctx.answerCbQuery("获取直播订阅列表失败");
      return;
    }
    
    const buttons = [];
    let message = `📺 <b>你的直播订阅列表 (${count} 个订阅)</b>`;
    if (count > limit) {
      message += ` - 显示 ${(page - 1) * limit + 1}-${Math.min(page * limit, count)}/${count}\n`;
    }
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      message += "\n";

      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅直播: ${creator.nickname || uid}`, `unsub_live:${uid}`)
      ]);
    }
    buttons.push([
      Markup.button.callback("🗑️ 取消所有直播订阅", "unsub_all_live")
    ]);
    
    if (count > limit) {
      const pageButtons = [];
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `live_page:${page - 1}`));
      }
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `live_page:${page + 1}`));
      }
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理直播分页请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

function numberWithCommas(x) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// isUserSubscribedToAuthor 函数已在文件开头定义

// 恢复作品按钮的辅助函数
async function restoreWorkButtons(ctx, uid, awemeId) {
  const chatId = ctx.chat.id;
  
  try {
    // 获取作品信息
    const { data: workData, error: workError } = await supabase
      .from("douyin")
      .select("file_id, title, aweme_id")
      .eq("aweme_id", awemeId)
      .limit(1);
    
    if (workError || !workData || workData.length === 0) {
      // 如果没有找到作品信息，只能恢复基本按钮
      const buttons = [];
      buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${awemeId}`));
      
      // 重新检查订阅状态
      const isSubscribedWork = await isUserSubscribedWork(chatId, uid);
      const isSubscribedLive = await isUserSubscribedLive(chatId, uid);
      
      if (isSubscribedWork) {
        buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${uid}:${awemeId}`));
      } else {
        buttons.push(Markup.button.callback("订阅作品", `sub:${uid}`));
      }
      
      // 只有在启用了直播功能时才显示直播订阅按钮
      if (SHOW_LIVE_BUTTONS) {
        if (isSubscribedLive) {
          buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${uid}`));
        } else {
          buttons.push(Markup.button.callback("订阅直播", `sub_live:${uid}`));
        }
      }
      
      const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));
      await ctx.editMessageReplyMarkup(markup.reply_markup);
      return;
    }
    
    const work = workData[0];
    const fileIds = work.file_id ? work.file_id.split(';').filter(id => id) : [];
    
    // 检测文件类型
    let zipFileId = null;
    let audioFileId = null;
    for (const fileId of fileIds) {
      if (detectFileType(fileId) === 'document') zipFileId = fileId;
      if (detectFileType(fileId) === 'audio') audioFileId = fileId;
    }
    
    // 重新生成按钮
    const buttons = [];
    buttons.push(Markup.button.url("打开", `https://www.douyin.com/video/${awemeId}`));
    buttons.push(Markup.button.callback("获取音乐", `music:${awemeId}`));
    if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${awemeId}`));
    
    // 重新检查订阅状态
    const isSubscribedWork = await isUserSubscribedWork(chatId, uid);
    const isSubscribedLive = await isUserSubscribedLive(chatId, uid);
    
    if (isSubscribedWork) {
      buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${uid}:${awemeId}`));
    } else {
      buttons.push(Markup.button.callback("订阅作品", `sub:${uid}`));
    }
    
    // 只有在启用了直播功能时才显示直播订阅按钮
    if (SHOW_LIVE_BUTTONS) {
      if (isSubscribedLive) {
        buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${uid}`));
      } else {
        buttons.push(Markup.button.callback("订阅直播", `sub_live:${uid}`));
      }
    }
    
    const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));
    
    // 只更新按钮，不改变消息内容
    await ctx.editMessageReplyMarkup(markup.reply_markup);
  } catch (error) {
    logger.error(`恢复作品按钮失败: ${error.message}`, error);
    throw error;
  }
}

// 处理作品消息中的取消订阅
bot.action(/^work_unsub:(.+):(.+)$/, async (ctx) => {
  const uid = ctx.match[1];
  const awemeId = ctx.match[2];
  const chatId = ctx.chat.id;
  
  try {
    const { data, error } = await supabase
      .from("douyin_user")
      .select("nickname, unique_id")
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      await ctx.answerCbQuery("获取创作者信息失败");
      return;
    }
    
    const creator = data && data.length > 0 ? data[0] : { nickname: "未知用户", unique_id: "" };
    const message = `确定要取消订阅 <b>${creator.nickname || uid}</b> 的作品吗？`;
    const buttons = [
      [
        Markup.button.callback("✅ 确定取消", `confirm_work_unsub:${uid}:${awemeId}`),
        Markup.button.callback("❌ 取消操作", `cancel_work_unsub:${uid}:${awemeId}`)
      ]
    ];
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理作品取消订阅请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

// 处理订阅列表中的取消订阅
bot.action(/^unsub:(.+)$/, async (ctx) => {
  const uid = ctx.match[1];
  const chatId = ctx.chat.id;
  
  try {
    const { data, error } = await supabase
      .from("douyin_user")
      .select("nickname, unique_id")
      .eq("uid", uid)
      .limit(1);
    
    if (error) {
      await ctx.answerCbQuery("获取创作者信息失败");
      return;
    }
    
    const creator = data && data.length > 0 ? data[0] : { nickname: "未知用户", unique_id: "" };
    const message = `确定要取消订阅 <b>${creator.nickname || uid}</b> 吗？`;
    
    // 尝试从消息中提取当前页码
    let currentPage = 1;
    const messageText = ctx.callbackQuery.message?.text || "";
    const pageMatch = messageText.match(/显示 (\d+)-(\d+)\/\d+/);
    if (pageMatch) {
      const startIndex = parseInt(pageMatch[1]);
      currentPage = Math.floor((startIndex - 1) / 10) + 1;
    }
    
    const buttons = [
      [
        Markup.button.callback("✅ 确定取消", `confirm_unsub:${uid}:${currentPage}`),
        Markup.button.callback("❌ 取消操作", `cancel_unsub:${currentPage}`)
      ]
    ];
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理取消订阅请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

// 处理作品中的确认取消订阅
// confirm_work_unsub 已不再需要，因为直接在 work_unsub 中处理

// cancel_work_unsub 已不再需要，因为直接在 work_unsub 中处理

bot.action(/^confirm_unsub:(.+)$/, async (ctx) => {
  const parts = ctx.match[1].split(':');
  const uid = parts[0];
  const currentPage = parts[1] ? parseInt(parts[1]) : 1;
  const chatId = ctx.chat.id;
  
  try {
    const { data: userData, error: userError } = await supabase
      .from("douyin_user")
      .select("nickname, unique_id")
      .eq("uid", uid)
      .limit(1);
    
    const creator = userData && userData.length > 0 ? userData[0] : { nickname: "未知用户", unique_id: "" };
    
    const { error } = await supabase
      .from("telegram_douyin_map")
      .delete()
      .eq("user_id", chatId)
      .eq("uid", uid);
    
    if (error) {
      await ctx.answerCbQuery("取消订阅失败，请稍后重试", { show_alert: true });
      return;
    }
    
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("uid", uid);
    
    if (!countError && count === 0) {
      await supabase
        .from("douyin_user")
        .update({ surveillance: false })
        .eq("uid", uid);
    }
    
    await ctx.editMessageText(
      `✅ 已成功取消订阅 <b>${creator.nickname || uid}</b>！\n\n使用 /subscriptions 查看你的订阅列表。`,
      { parse_mode: 'HTML' }
    );
    await ctx.answerCbQuery("已成功取消订阅", { show_alert: true });
  } catch (error) {
    logger.error(`确认取消订阅出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试", { show_alert: true });
  }
});

bot.action(/^cancel_unsub(?::(\d+))?$/, async (ctx) => {
  const chatId = ctx.chat.id;
  const page = ctx.match[1] ? parseInt(ctx.match[1]) : 1;
  
  try {
    await ctx.answerCbQuery("已取消操作");
    
    // 恢复到订阅列表页面
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      await ctx.editMessageText("获取订阅列表时发生错误，请使用 /subscriptions 重试。");
      return;
    }
    
    const limit = 10;
    
    const { data: subscriptions, error } = await supabase
      .from("telegram_douyin_map")
      .select(`
        uid,
        douyin_user!inner(
          nickname,
          unique_id,
          avatar_thumb_url,
          follower_count,
          total_favorited
        )
      `)
      .eq("user_id", chatId)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error || !subscriptions || subscriptions.length === 0) {
      await ctx.editMessageText("你当前没有订阅任何抖音创作者。\n\n可以通过点击视频下方的「订阅」按钮来订阅创作者。");
      return;
    }
    
    const buttons = [];
    let message = `📋 <b>你的作品订阅列表 (${count} 个订阅)</b>`;
    if (count > limit) {
      message += ` - 显示 ${(page - 1) * limit + 1}-${Math.min(page * limit, count)}/${count}\n`;
    }
    message += "\n\n";
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const creator = sub.douyin_user;
      const uid = sub.uid;
      
      message += `<b>${(page - 1) * limit + i + 1}. ${creator.nickname || "未知用户"}</b>\n`;
      if (creator.unique_id) {
        message += `抖音号: ${creator.unique_id}\n`;
      }
      if (creator.follower_count) {
        message += `粉丝数: ${numberWithCommas(creator.follower_count)}\n`;
      }
      if (creator.total_favorited) {
        message += `获赞数: ${numberWithCommas(creator.total_favorited)}\n`;
      }
      message += "\n";
      
      buttons.push([
        Markup.button.callback(`🗑️ 取消订阅作品: ${creator.nickname || uid}`, `unsub:${uid}`)
      ]);
    }
    buttons.push([
      Markup.button.callback("🗑️ 取消所有订阅", "unsub_all")
    ]);
    
    if (count > limit) {
      const pageButtons = [];
      if (page > 1) {
        pageButtons.push(Markup.button.callback("◀️ 上一页", `douyin_page:${page - 1}`));
      }
      if (page * limit < count) {
        pageButtons.push(Markup.button.callback("▶️ 下一页", `douyin_page:${page + 1}`));
      }
      if (pageButtons.length > 0) {
        buttons.push(pageButtons);
      }
    }
    
    const markup = Markup.inlineKeyboard(buttons);
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
  } catch (error) {
    logger.error(`处理取消操作时出错: ${error.message}`, error);
    await ctx.editMessageText("恢复订阅列表时发生错误，请使用 /subscriptions 重试。");
  }
});

bot.action('unsub_all', async (ctx) => {
  const chatId = ctx.chat.id;
  
  try {
    const { count, error: countError } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId);
    
    if (countError) {
      await ctx.answerCbQuery("获取订阅数量失败");
      return;
    }
    
    if (count === 0) {
      await ctx.answerCbQuery("你当前没有订阅任何创作者", { show_alert: true });
      return;
    }
    
    const message = `⚠️ <b>确定要取消所有 ${count} 个订阅吗？</b>\n\n此操作无法撤销，你将不再收到这些创作者的作品更新。`;
    const buttons = [
      [
        Markup.button.callback("✅ 确定取消所有", "confirm_unsub_all"),
        Markup.button.callback("❌ 取消操作", "cancel_unsub")
      ]
    ];
    
    const markup = Markup.inlineKeyboard(buttons);
    
    await ctx.editMessageText(message, {
      parse_mode: 'HTML',
      ...markup
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`处理取消所有订阅请求出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试");
  }
});

bot.action('confirm_unsub_all', async (ctx) => {
  const chatId = ctx.chat.id;
  
  try {
    const { data: subscriptions, error: getError } = await supabase
      .from("telegram_douyin_map")
      .select("uid")
      .eq("user_id", chatId);
    
    if (getError) {
      await ctx.answerCbQuery("获取订阅信息失败，请稍后重试", { show_alert: true });
      return;
    }
    
    const { error } = await supabase
      .from("telegram_douyin_map")
      .delete()
      .eq("user_id", chatId);
    
    if (error) {
      await ctx.answerCbQuery("取消订阅失败，请稍后重试", { show_alert: true });
      return;
    }
    
    if (subscriptions && subscriptions.length > 0) {
      for (const sub of subscriptions) {
        const { count, error: countError } = await supabase
          .from("telegram_douyin_map")
          .select("*", { count: 'exact', head: true })
          .eq("uid", sub.uid);
        
        if (!countError && count === 0) {
          await supabase
            .from("douyin_user")
            .update({ surveillance: false })
            .eq("uid", sub.uid);
        }
      }
    }
    
    await ctx.editMessageText(
      `✅ 已成功取消所有订阅！\n\n你不会再收到这些创作者的作品更新。`,
      { parse_mode: 'HTML' }
    );
    
    await ctx.answerCbQuery("已成功取消所有订阅", { show_alert: true });
  } catch (error) {
    logger.error(`确认取消所有订阅出错: ${error.message}`, error);
    await ctx.answerCbQuery("处理请求失败，请稍后重试", { show_alert: true });
  }
});

// =============== 回调按钮处理 ===============
bot.on('callback_query', async (ctx) => {
  const callbackData = ctx.callbackQuery.data;
  logger.info(`收到callback_query: ${callbackData}, 来自用户: ${ctx.from.id}`);
  
  // 不要立即响应，让具体处理函数决定何时响应
  
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) {
    logger.info(`忽略来自 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 的回调: ${callbackData}`);
    return;
  }

  const chatId = ctx.chat.id;
  const fromUsername = ctx.from.username || "";
  const userRecord = await getOrCreateUser(chatId, fromUsername);

  const sendAny = userRecord.send_any !== undefined ? userRecord.send_any : true;
  const todayLimit = userRecord.today || 10;
  const alreadyUsed = userRecord.already || 0;

  if (!sendAny || alreadyUsed >= todayLimit) {
    logger.info(`用户 ${chatId} 达到配额上限，拒绝处理回调: ${callbackData}`);
    await ctx.answerCbQuery("你已达到每日配额上限。", { show_alert: true });
    const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
    setTimeout(async () => {
      try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
    }, 10000);
    return;
  }

  const data = callbackData;

  // ------ 获取更多作者作品 (原 "author:") 省略。若需要可再加 ------

  // ------ 下载音乐 ------
  if (data.startsWith("music:")) {
    const awemeId = data.substring(6);
    if (!awemeId) {
      await ctx.answerCbQuery("无效的作品ID", { show_alert: true });
      return;
    }

    await ctx.answerCbQuery("正在查找音乐...");

    try {
      const { data: workData, error: dbError } = await supabase
        .from("douyin")
        .select("file_id, high_quality")
        .eq("aweme_id", awemeId)
        .limit(1);

      if (dbError) {
        logger.error(`回调查询作品 ${awemeId} 的 file_id 失败: ${dbError.message}`, dbError);
        await ctx.answerCbQuery("查找音乐信息失败", { show_alert: true });
        return;
      }
      if (!workData || workData.length === 0) {
        await ctx.answerCbQuery("未找到该作品的信息", { show_alert: true });
        return;
      }

      // 优先使用 high_quality 字段，如果为空或为'done'则使用 file_id 字段
      let fileIdsString = null;
      if (workData[0].high_quality && workData[0].high_quality.trim() !== "" && workData[0].high_quality.trim() !== "done") {
        fileIdsString = workData[0].high_quality;
        logger.info(`用户 ${chatId} 请求音乐，使用 high_quality 字段: ${fileIdsString}`);
      } else if (workData[0].file_id && workData[0].file_id.trim() !== "") {
        fileIdsString = workData[0].file_id;
        logger.info(`用户 ${chatId} 请求音乐，使用 file_id 字段: ${fileIdsString} (high_quality=${workData[0].high_quality})`);
      } else {
        await ctx.answerCbQuery("未找到该作品的音乐文件信息", { show_alert: true });
        return;
      }

      const fileIds = fileIdsString.split(';');
      let audioFileId = null;
      for (const fileId of fileIds) {
        if (detectFileType(fileId) === 'audio') {
          audioFileId = fileId;
          break;
        }
      }

      if (!audioFileId) {
        await ctx.answerCbQuery("该作品没有单独的音频文件");
        logger.warning(`用户 ${chatId} 请求作品 ${awemeId} 的音乐，但在数据库记录中未找到 audio file_id，检查的字段: ${fileIdsString}`);
        
        // 发送引导消息
        const message = "该作品没有单独的音频文件，请使用 <a href=\"https://t.me/artworkcoverbot\">media tool</a> 提取该视频的音频。可以直接转发视频到该bot。";
        const replyMsg = await ctx.reply(message, { parse_mode: 'HTML' });
        
        // 10秒后删除消息
        setTimeout(async () => {
          try {
            await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id);
          } catch (e) {
            logger.debug(`删除引导消息失败: ${e.message}`);
          }
        }, 10000);
        
        return;
      }

      await ctx.replyWithAudio(audioFileId);
      await ctx.answerCbQuery("音乐已发送");
      await updateUser(chatId, { already: alreadyUsed + 1 });
      logger.info(`用户 ${chatId} 点击按钮获取了作品 ${awemeId} 的音乐 (file_id: ${audioFileId})`);
    } catch (error) {
      logger.error(`发送音乐 (awemeId: ${awemeId}, 从DB获取) 失败: ${error.message}`, error);
      try {
        await ctx.reply("抱歉，发送音乐时遇到问题，请稍后再试。");
      } catch (replyError) {
        logger.error(`无法向用户 ${chatId} 发送音乐错误通知: ${replyError.message}`);
      }
    }
  }
  // ------ 订阅作者 ------
  else if (data.startsWith("sub:")) {
    logger.info(`匹配到订阅作品回调: ${data}`);
    const uidToSubscribe = data.substring(4); // Directly get UID

    if (!uidToSubscribe) {
      logger.error(`订阅作品失败：无效的UID - data: ${data}`);
      await ctx.answerCbQuery("无效的作者UID", { show_alert: true });
      return;
    }
    
    logger.info(`准备订阅作者 UID: ${uidToSubscribe}, 用户: ${chatId}`);
    
    // User quota and send_any check (remains the same)
    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false }); // Ensure send_any is updated if limit is hit
      await ctx.answerCbQuery("你已达到每日配额上限。", { show_alert: true });
      const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      setTimeout(async () => {
        try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      }, 10000);
      return;
    }
    
    // Subscription limit check for non-premium users (remains the same)
    const isPremium = isPremiumUser(userRecord);
    if (!isPremium) {
      const isAllowedToSubscribe = await checkSubscriptionLimit(chatId, ctx);
      if (!isAllowedToSubscribe) {
        // checkSubscriptionLimit already sends its own message
        return;
      }
    }
    
    try {
      // Fetch author details from douyin_user using UID
      const { data: authorDataArray, error: authorQueryError } = await supabase
        .from("douyin_user")
        .select("*") // Select all needed fields, esp. nickname, unique_id, sec_uid
        .eq("uid", uidToSubscribe)
        .limit(1);
      
      if (authorQueryError) {
        logger.error(`订阅回调：查询作者信息失败 (uid: ${uidToSubscribe}): ${authorQueryError.message}`, authorQueryError);
        await ctx.answerCbQuery("查询作者信息时出错，请稍后再试。", { show_alert: true });
        return;
      }
      
      if (!authorDataArray || authorDataArray.length === 0) {
        logger.warning(`订阅回调：数据库中未找到作者信息 (uid: ${uidToSubscribe})。可能需要用户通过作品链接重新触发作者信息保存。`);
        await ctx.answerCbQuery("订阅失败：作者信息不完整或未在库中找到。请尝试通过该作者的某一个作品链接再次操作，或稍后再试。", { show_alert: true });
        return;
      }
      
      const author = authorDataArray[0];
      
      // 检查作品数量
      let awemeCount = author.aweme_count;
      
      // 如果数据库中没有作品数量（null、0或未定义），且有sec_uid，则通过API获取
      if ((!awemeCount || awemeCount === 0) && author.sec_uid) {
        logger.info(`订阅回调：数据库中没有作品数量，正在通过API获取...`);
        awemeCount = await fetchUserAwemeCount(author.sec_uid);
        
        // 将获取到的作品数量存入数据库
        if (awemeCount !== null) {
          const { error: updateAwemeCountError } = await supabase
            .from("douyin_user")
            .update({ aweme_count: awemeCount })
            .eq("uid", uidToSubscribe);
          
          if (updateAwemeCountError) {
            logger.error(`订阅回调：更新作者作品数量失败 (uid: ${uidToSubscribe}): ${updateAwemeCountError.message}`, updateAwemeCountError);
          } else {
            logger.info(`订阅回调：成功更新作者 ${author.nickname || uidToSubscribe} 的作品数量为 ${awemeCount}`);
          }
        }
      } else if (awemeCount && awemeCount > 0) {
        logger.info(`订阅回调：使用数据库中的作品数量: ${awemeCount}`);
      }
      
      // 检查作品数量是否超过限制
      if (awemeCount !== null && awemeCount > 50000) {
        logger.warning(`订阅回调：作者 ${author.nickname || uidToSubscribe} 的作品数量 (${awemeCount}) 超过50000，拒绝订阅`);
        await ctx.answerCbQuery("该作者无法订阅", { show_alert: true });
        return;
      }
      
      if (!author.sec_uid && (!awemeCount || awemeCount === 0)) {
        logger.warning(`订阅回调：作者 ${uidToSubscribe} 缺少sec_uid，无法检查作品数量`);
      }
      // Ensure sec_uid is present if any subsequent logic strictly needs it, though primary operations now use UID.
      if (!author.sec_uid) {
          logger.warning(`订阅回调：作者信息 (uid: ${uidToSubscribe}) 缺少 sec_uid。某些操作可能受限。`);
          // Depending on strictness, might alert user or proceed if sec_uid is not critical for remaining steps.
      }
      
      // Check if already subscribed (uses uidToSubscribe)
      const { data: existingMap, error: checkError } = await supabase
        .from("telegram_douyin_map")
        .select("*")
        .eq("user_id", chatId)
        .eq("uid", uidToSubscribe)
        .limit(1);
        
      if (checkError) {
        logger.error(`订阅回调：检查订阅映射失败 (uid: ${uidToSubscribe}): ${checkError.message}`, checkError);
        await ctx.answerCbQuery("检查订阅状态时出错。", { show_alert: true });
        return;
      }
      
      if (existingMap && existingMap.length > 0) {
        // 如果已有记录但 douyin 字段未开启，则更新
        const row = existingMap[0];
        if (!row.douyin) {
          const { error: updateMapError } = await supabase.from("telegram_douyin_map")
            .update({ douyin: true })
            .eq("user_id", chatId)
            .eq("uid", uidToSubscribe);
          
          if (updateMapError) {
            logger.error(`订阅回调：更新订阅映射失败 (uid: ${uidToSubscribe}): ${updateMapError.message}`, updateMapError);
            await ctx.answerCbQuery(`更新订阅状态失败: ${author.nickname || author.unique_id || uidToSubscribe}`, { show_alert: true });
            return;
          }
        } else {
          await ctx.answerCbQuery(`你已经订阅了该作者: ${author.nickname || author.unique_id || uidToSubscribe}`, { show_alert: true });
          return;
        }
      }

      // Mark surveillance in douyin_user table (using UID)
      const { error: updateError } = await supabase
        .from("douyin_user")
        .update({ surveillance: true })
        .eq("uid", uidToSubscribe); // Changed from sec_uid

      if (updateError) {
        logger.error(`订阅回调：更新作者surveillance字段失败 (uid: ${uidToSubscribe}): ${updateError.message}`, updateError);
      }
      
      if (!(existingMap && existingMap.length > 0)) {
        // 新增行
        const { error: mapError } = await supabase
          .from("telegram_douyin_map")
          .insert([{ user_id: chatId, uid: uidToSubscribe, douyin: true }]);

        if (mapError) {
          logger.error(`订阅回调：创建用户订阅映射失败 (uid: ${uidToSubscribe}): ${mapError.message}`, mapError);
          await ctx.answerCbQuery(`订阅关系建立失败: ${author.nickname || author.unique_id || uidToSubscribe}`, { show_alert: true });
          return;
        }
      }
      
      logger.info(`已创建用户 ${chatId} 订阅作者 ${uidToSubscribe} (${author.nickname || ""}) 的映射`);
      
      // 直接发送成功消息，不再调用 answerCbQuery
      const successMessage = `✅ 成功订阅作者: ${author.nickname || author.unique_id || uidToSubscribe}`;
      logger.info(`订阅成功: ${successMessage}`);
      
      // 更新按钮而不是发送新消息
      try {
        const buttons = [[]];
        const messageId = ctx.callbackQuery.message.message_id;
        const currentText = ctx.callbackQuery.message.text || ctx.callbackQuery.message.caption || "";
        
        // 检查原消息中的按钮
        if (ctx.callbackQuery.message.reply_markup && ctx.callbackQuery.message.reply_markup.inline_keyboard) {
          const originalButtons = ctx.callbackQuery.message.reply_markup.inline_keyboard;
          for (const row of originalButtons) {
            const newRow = [];
            for (const btn of row) {
              if (btn.callback_data === `sub:${uidToSubscribe}`) {
                // 替换订阅按钮为取消订阅按钮
                newRow.push(Markup.button.callback("取消订阅作品", `work_unsub:${uidToSubscribe}:${ctx.callbackQuery.message.text.match(/video\/(\d+)/)?.[1] || ''}`));
              } else {
                newRow.push(btn);
              }
            }
            if (newRow.length > 0) buttons.push(newRow);
          }
        }
        
        if (ctx.callbackQuery.message.caption) {
          await ctx.editMessageCaption(currentText + `\n\n${successMessage}`, {
            reply_markup: { inline_keyboard: buttons },
            parse_mode: 'HTML'
          });
        } else {
          await ctx.editMessageText(currentText + `\n\n${successMessage}`, {
            reply_markup: { inline_keyboard: buttons },
            parse_mode: 'HTML'
          });
        }
      } catch (editError) {
        logger.error(`更新消息失败: ${editError.message}`, editError);
        // 如果更新失败，至少发送一条新消息通知用户
        const notifyMsg = await ctx.reply(successMessage);
        setTimeout(async () => {
          try { await ctx.telegram.deleteMessage(ctx.chat.id, notifyMsg.message_id); } catch (e) {}
        }, 5000);
      }
      
      // 成功订阅后响应回调查询
      await ctx.answerCbQuery(successMessage);
      
      await updateUser(chatId, { already: alreadyUsed + 1 }); // Increment usage only on successful subscription completion
    } catch (error) {
      logger.error(`订阅作者 (uid: ${uidToSubscribe}) 出错: ${error.message}`, error);
      await ctx.answerCbQuery("订阅时发生未知错误。", { show_alert: true });
    }
  }
  // ------ 获取ZIP文件 ------
  else if (data.startsWith("zip:")) {
    if (!sendAny || alreadyUsed >= todayLimit) {
      await ctx.answerCbQuery();
      const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      setTimeout(async () => {
        try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      }, 10000);
      return;
    }
    
    const awemeId = data.substring(4);
    if (!awemeId) {
      await ctx.answerCbQuery("无效的作品ID", { show_alert: true });
      return;
    }

    try {
      const { data: workData, error: dbError } = await supabase
        .from("douyin")
        .select("file_id, high_quality")
        .eq("aweme_id", awemeId)
        .limit(1);
      
      if (dbError) {
        logger.error(`回调查询作品 ${awemeId} 的 file_id 失败: ${dbError.message}`, dbError);
        await ctx.answerCbQuery("查找ZIP文件信息失败", { show_alert: true });
        return;
      }
      if (!workData || workData.length === 0) {
        await ctx.answerCbQuery("未找到该作品的信息", { show_alert: true });
        return;
      }
      
      // 优先使用 high_quality 字段，如果为空或为'done'则使用 file_id 字段
      let fileIdsString = null;
      if (workData[0].high_quality && workData[0].high_quality.trim() !== "" && workData[0].high_quality.trim() !== "done") {
        fileIdsString = workData[0].high_quality;
        logger.info(`用户 ${chatId} 请求ZIP文件，使用 high_quality 字段: ${fileIdsString}`);
      } else if (workData[0].file_id && workData[0].file_id.trim() !== "") {
        fileIdsString = workData[0].file_id;
        logger.info(`用户 ${chatId} 请求ZIP文件，使用 file_id 字段: ${fileIdsString} (high_quality=${workData[0].high_quality})`);
      } else {
        await ctx.answerCbQuery("未找到该作品的ZIP文件信息", { show_alert: true });
        return;
      }
      
      const fileIds = fileIdsString.split(';');
      let zipFileId = null;
      for (const fileId of fileIds) {
        if (fileId && detectFileType(fileId) === 'document') {
          zipFileId = fileId;
          break;
        }
      }
      
      if (!zipFileId) {
        await ctx.answerCbQuery("无法从记录中找到ZIP文件", { show_alert: true });
        logger.warning(`用户 ${chatId} 请求作品 ${awemeId} 的ZIP，但未找到 document file_id，检查的字段: ${fileIdsString}`);
        return;
      }
      
      await ctx.answerCbQuery("正在发送ZIP文件...");
      await ctx.replyWithDocument(zipFileId);
      logger.info(`用户 ${chatId} 点击按钮获取了作品 ${awemeId} 的ZIP (file_id: ${zipFileId})`);
      
      await updateUser(chatId, { already: alreadyUsed + 1 });
    } catch (error) {
      logger.error(`发送ZIP文件 (awemeId: ${awemeId}, 从DB获取) 失败: ${error.message}`, error);
      try {
        await ctx.reply("抱歉，发送ZIP文件时遇到问题，请稍后再试。");
      } catch (replyError) {
        logger.error(`无法向用户 ${chatId} 发送ZIP错误通知: ${replyError.message}`);
      }
    }
  }
  else if (data.startsWith("sub_live:")) {
    const uidLive = data.substring(9);

    if (!uidLive) {
      await ctx.answerCbQuery("无效的作者UID", { show_alert: true });
      return;
    }
    
    // 不要立即响应，等待处理结果后再响应

    try {
      // 先查询作者信息以获取 sec_uid
      const { data: authorData, error: authorError } = await supabase
        .from("douyin_user")
        .select("*")
        .eq("uid", uidLive)
        .limit(1);
      
      if (authorError || !authorData || authorData.length === 0) {
        logger.error(`订阅直播：查询作者信息失败 (uid: ${uidLive})`);
        await ctx.answerCbQuery("查询作者信息失败，请稍后再试", { show_alert: true });
        return;
      }
      
      const author = authorData[0];
      
      // 更新 douyin_user.record = true
      await supabase.from("douyin_user").update({ record: true }).eq("uid", uidLive);

      // 检查是否已存在映射
      const { data: existMap, error: existErr } = await supabase
        .from("telegram_douyin_map")
        .select("*")
        .eq("user_id", chatId)
        .eq("uid", uidLive)
        .limit(1);

      if (existErr) {
        logger.error(`检查订阅直播映射失败: ${existErr.message}`, existErr);
        await ctx.answerCbQuery("处理失败，请稍后再试", { show_alert: true });
        return;
      }

      if (existMap && existMap.length > 0) {
        // 更新 live 字段
        await supabase.from("telegram_douyin_map")
          .update({ live: true })
          .eq("user_id", chatId)
          .eq("uid", uidLive);
      } else {
        // 插入新行
        await supabase.from("telegram_douyin_map")
          .insert([{ user_id: chatId, uid: uidLive, live: true }]);
      }

      // 更新按钮而不是发送新消息
      const successMessage = "✅ 已成功订阅直播通知";
      logger.info(`订阅直播成功: ${successMessage}`);
      
      try {
        const buttons = [[]];
        
        if (ctx.callbackQuery.message.reply_markup && ctx.callbackQuery.message.reply_markup.inline_keyboard) {
          const originalButtons = ctx.callbackQuery.message.reply_markup.inline_keyboard;
          for (const row of originalButtons) {
            const newRow = [];
            for (const btn of row) {
              if (btn.callback_data === `sub_live:${uidLive}`) {
                // 替换订阅直播按钮为取消订阅按钮
                newRow.push(Markup.button.callback("取消订阅直播", `unsub_live:${uidLive}`));
              } else {
                newRow.push(btn);
              }
            }
            if (newRow.length > 0) buttons.push(newRow);
          }
        }
        
        const currentText = ctx.callbackQuery.message.text || ctx.callbackQuery.message.caption || "";
        
        if (ctx.callbackQuery.message.caption) {
          await ctx.editMessageCaption(currentText + `\n\n${successMessage}`, {
            reply_markup: { inline_keyboard: buttons },
            parse_mode: 'HTML'
          });
        } else {
          await ctx.editMessageText(currentText + `\n\n${successMessage}`, {
            reply_markup: { inline_keyboard: buttons },
            parse_mode: 'HTML'
          });
        }
      } catch (editError) {
        logger.error(`更新消息失败: ${editError.message}`, editError);
        // 如果更新失败，发送一个会自动删除的消息
        const notifyMsg = await ctx.reply(successMessage);
        setTimeout(async () => {
          try { await ctx.telegram.deleteMessage(ctx.chat.id, notifyMsg.message_id); } catch (e) {}
        }, 3000);
      }
      
      // 成功订阅直播后响应回调查询
      await ctx.answerCbQuery(successMessage);
    } catch (err) {
      logger.error(`订阅直播出错: ${err.message}`, err);
      try {
        await ctx.answerCbQuery("订阅直播时出错", { show_alert: true });
      } catch (answerErr) {
        logger.error(`发送错误提示失败: ${answerErr.message}`, answerErr);
      }
    }
  }
  else if (data.startsWith("unsub_live:")) {
    const uidLive = data.substring(11);
    if (!uidLive) {
      await ctx.answerCbQuery("无效的作者UID", { show_alert: true });
      return;
    }
    
    // 立即响应
    await ctx.answerCbQuery("正在取消订阅直播...");

    try {
      const { data: mapRows, error: mapErr } = await supabase
        .from("telegram_douyin_map")
        .select("douyin, live")
        .eq("user_id", chatId)
        .eq("uid", uidLive)
        .limit(1);

      if (mapErr) {
        logger.error(`查询映射失败: ${mapErr.message}`, mapErr);
        await ctx.answerCbQuery("处理失败，请稍后再试", { show_alert: true });
        return;
      }

      if (!mapRows || mapRows.length === 0) {
        await ctx.answerCbQuery("尚未订阅直播", { show_alert: true });
        return;
      }

      // 不管是否有作品订阅，都只更新 live 字段为 false
      await supabase.from("telegram_douyin_map")
        .update({ live: false })
        .eq("user_id", chatId)
        .eq("uid", uidLive);

      // 如果该作者没有其他订阅者的 live=true，则把 douyin_user.record 置为 false (可选)
      try {
        const { count } = await supabase
          .from("telegram_douyin_map")
          .select("*", { count: 'exact', head: true })
          .eq("uid", uidLive)
          .eq("live", true);
        if (count === 0) {
          await supabase.from("douyin_user").update({ record: false }).eq("uid", uidLive);
        }
      } catch (innerErr) {}

      // 更新按钮而不是发送新消息
      const successMessage = "❌ 已取消直播通知订阅";
      logger.info(`取消订阅直播成功: ${successMessage}`);
      
      try {
        const buttons = [[]];
        
        if (ctx.callbackQuery.message.reply_markup && ctx.callbackQuery.message.reply_markup.inline_keyboard) {
          const originalButtons = ctx.callbackQuery.message.reply_markup.inline_keyboard;
          for (const row of originalButtons) {
            const newRow = [];
            for (const btn of row) {
              if (btn.callback_data === `unsub_live:${uidLive}`) {
                // 替换取消订阅按钮为订阅按钮
                newRow.push(Markup.button.callback("订阅直播", `sub_live:${uidLive}`));
              } else {
                newRow.push(btn);
              }
            }
            if (newRow.length > 0) buttons.push(newRow);
          }
        }
        
        const currentText = ctx.callbackQuery.message.text || ctx.callbackQuery.message.caption || "";
        
        if (ctx.callbackQuery.message.caption) {
          await ctx.editMessageCaption(currentText + `\n\n${successMessage}`, {
            reply_markup: { inline_keyboard: buttons },
            parse_mode: 'HTML'
          });
        } else {
          await ctx.editMessageText(currentText + `\n\n${successMessage}`, {
            reply_markup: { inline_keyboard: buttons },
            parse_mode: 'HTML'
          });
        }
      } catch (editError) {
        logger.error(`更新消息失败: ${editError.message}`, editError);
        // 如果更新失败，发送一个会自动删除的消息
        const notifyMsg = await ctx.reply(successMessage);
        setTimeout(async () => {
          try { await ctx.telegram.deleteMessage(ctx.chat.id, notifyMsg.message_id); } catch (e) {}
        }, 3000);
      }
    } catch (err) {
      logger.error(`取消直播订阅出错: ${err.message}`, err);
      await ctx.answerCbQuery("取消订阅时出错", { show_alert: true });
    }
  }
  else {
    logger.warning(`未匹配到任何回调处理器: ${data}`);
    await ctx.answerCbQuery("未知操作", { show_alert: true });
  }
});

// =============== 处理文本消息：抖音链接 ===============
// 使用Worker池处理抖音链接
async function processDouyinWithWorker(ctx, douyinUrl, awemeId) {
  const chatId = ctx.chat.id;
  const processingMsg = await telegramQueue.sendText(chatId, "⏳ 正在排队处理中...", { priority: 1 });
  
  try {
    // 准备任务数据
    const taskData = {
      id: `task-${chatId}-${awemeId}-${Date.now()}`,
      type: 'process-douyin',
      url: douyinUrl,
      chatId: chatId,
      awemeId: awemeId,
      messageId: processingMsg
    };

    // 如果使用RabbitMQ队列
    if (taskQueue && process.env.USE_RABBITMQ === 'true') {
      await taskQueue.sendPriorityTask('douyin-tasks', {
        type: 'process-douyin',
        data: taskData,
        metadata: {
          chatId: chatId,
          priority: ctx.from?.is_premium ? 5 : 1 // VIP用户优先级更高
        }
      }, ctx.from?.is_premium ? 5 : 1);
      
      logger.info(`任务已添加到RabbitMQ队列: ${taskData.id}`);
    } else {
      // 直接使用Worker池
      logger.info(`开始执行Worker任务: ${taskData.id}`);
      const result = await workerPool.execute(taskData);
      logger.info(`Worker任务完成: ${taskData.id}, 结果:`, JSON.stringify(result));
      await handleWorkerResult(result, taskData);
    }
  } catch (error) {
    logger.error(`处理抖音链接失败: ${error.message}`, error);
    // 不再尝试编辑消息，而是发送新消息
    await telegramQueue.sendText(chatId, "❌ 处理失败，请稍后再试");
  }
}

bot.on('text', async (ctx) => {
  console.log(`=== TEXT HANDLER CALLED === Bot launched: ${bot.botInfo ? 'YES' : 'NO'}`);
  console.log(`Message text: ${ctx.message.text}`);
  console.log(`Message date: ${new Date(ctx.message.date * 1000).toISOString()}`);
  
  // 醒目打印用户信息
  console.log(`\n========== 用户消息 ==========`);
  console.log(`🔹 用户ID: ${ctx.from.id}`);
  console.log(`🔹 用户名: @${ctx.from.username || '未设置'}`);
  console.log(`🔹 姓名: ${ctx.from.first_name} ${ctx.from.last_name || ''}`);
  console.log(`🔹 聊天ID: ${ctx.chat.id}`);
  console.log(`🔹 聊天类型: ${ctx.chat.type}`);
  console.log(`==============================\n`);
  
  if (ctx.chat?.id === STORAGE_CHANNEL_ID) {
    logger.info(`忽略来自 STORAGE_CHANNEL_ID (${STORAGE_CHANNEL_ID}) 的文本消息: ${ctx.message.text}`);
    return;
  }

  try {
    if (ctx.message.text.startsWith('/')) {
      return;
    }

    const chatId = ctx.chat.id;
    const fromUsername = ctx.from.username || null;
    const userRecord = await getOrCreateUser(chatId, fromUsername);

    const sendAny = userRecord.send_any !== undefined ? userRecord.send_any : true;
    const todayLimit = userRecord.today || 10;
    const alreadyUsed = userRecord.already || 0;

    if (!sendAny || alreadyUsed >= todayLimit) {
      await updateUser(chatId, { send_any: false });
      const replyMsg = await ctx.reply("你已达到每日配额上限，使用量将在下一个23:00清空。");
      setTimeout(async () => {
        try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      }, 10000);
      return;
    }

    const text = ctx.message.text.trim();
    if (!text) {
      return;
    }

    // 检查是否是用户主页链接，如果是则判定为无效链接
    const userUrl = extractDouyinUserUrl(text);
    if (userUrl) {
      await ctx.reply("抖音用户主页链接无效，请发送单个作品链接。");
      return;
    }

    // 单作品链接
    const douyinUrl = extractDouyinVideoUrl(text);
    if (douyinUrl) {
      // 打印处理抖音链接的用户信息
      logger.info(`\n🎬 处理抖音链接 - 用户: @${ctx.from.username || ctx.from.id} (${ctx.from.first_name}) - URL: ${douyinUrl}`);
      
      // 如果Worker池已初始化，使用Worker池处理
      if (workerPool && workerPool.getStats().activeWorkers > 0) {
        let awemeId = null;
        
        // 先尝试从URL直接解析 awemeId
        const videoIdMatch = douyinUrl.match(/\/video\/(\d+)/);
        const noteIdMatch = douyinUrl.match(/\/note\/(\d+)/);
        const lvDetailMatch = douyinUrl.match(/\/lvdetail\/(\d+)/);
        
        if (videoIdMatch && videoIdMatch[1]) {
          awemeId = videoIdMatch[1];
        } else if (noteIdMatch && noteIdMatch[1]) {
          awemeId = noteIdMatch[1];
        } else if (lvDetailMatch && lvDetailMatch[1]) {
          awemeId = lvDetailMatch[1];
        }
        
        // 使用Worker池处理
        await processDouyinWithWorker(ctx, douyinUrl, awemeId);
        return;
      }
      
      // 如果Worker池未初始化，使用原有的处理逻辑
      const processingMsg = await ctx.reply("正在排队处理...");
      let awemeId = null; // 将 awemeId 的声明提到 try 外部

      try {
        // 先尝试从URL直接解析 awemeId
        let isStandardVideoUrl = false;
        
        const videoIdMatch = douyinUrl.match(/\/video\/(\d+)/);
        const noteIdMatch = douyinUrl.match(/\/note\/(\d+)/);
        const lvDetailMatch = douyinUrl.match(/\/lvdetail\/(\d+)/);
        
        if (videoIdMatch && videoIdMatch[1]) {
          awemeId = videoIdMatch[1];
          isStandardVideoUrl = true;
          logger.info(`[AWEME_ID_EXTRACT] 从标准视频链接直接提取 aweme_id: ${awemeId}，URL: ${douyinUrl}`);
        } else if (noteIdMatch && noteIdMatch[1]) {
          awemeId = noteIdMatch[1];
          isStandardVideoUrl = true;
          logger.info(`[AWEME_ID_EXTRACT] 从标准笔记链接直接提取 aweme_id: ${awemeId}，URL: ${douyinUrl}`);
        } else if (lvDetailMatch && lvDetailMatch[1]) {
          awemeId = lvDetailMatch[1];
          isStandardVideoUrl = true;
          logger.info(`[AWEME_ID_EXTRACT] 从长视频链接直接提取 ID: ${awemeId}，URL: ${douyinUrl}`);
        } else {
          logger.info(`[AWEME_ID_EXTRACT] 无法从URL直接提取aweme_id，URL: ${douyinUrl}，需要调用API解析`);
        }

        let existingWorkData = null;
        let isWorkExistInDB = false;
        
        // 如果能提取到awemeId，先查数据库
        if (awemeId) {
          logger.info(`[DB_QUERY] 开始查询数据库，aweme_id: ${awemeId}`);
          const { data: existingData, error: checkError } = await supabase
            .from("douyin")
            .select("*")
            .eq("aweme_id", awemeId)
            .limit(1);
          
          if (checkError) {
            logger.error(`[DB_QUERY] 数据库查询失败，aweme_id: ${awemeId}，错误: ${checkError.message}`);
          } else if (existingData && existingData.length > 0) {
            existingWorkData = existingData[0];
            isWorkExistInDB = true;
            logger.info(`[DB_QUERY] 作品 ${awemeId} 已存在于数据库，记录ID: ${existingWorkData.id}，high_quality: ${existingWorkData.high_quality ? '有数据(' + existingWorkData.high_quality.length + '字符)' : '空'}，file_id: ${existingWorkData.file_id ? '有数据(' + existingWorkData.file_id.length + '字符)' : '空'}`);
          } else {
            logger.info(`[DB_QUERY] 作品 ${awemeId} 不存在于数据库，需要API解析`);
          }
        } else {
          logger.info(`[DB_QUERY] 没有aweme_id，跳过数据库查询，将直接调用API`);
        }

        if (existingWorkData) {
          logger.info(`[DB_LOGIC] 处理数据库现有记录，aweme_id: ${awemeId}`);
          
          let fileIdsToUseForSend = [];
          let sourceOfFileIdsInfo = ""; // For logging

          logger.info(`[FILE_ID_CHECK] 作品 ${awemeId} 检查文件ID，high_quality: '${existingWorkData.high_quality}' (长度: ${existingWorkData.high_quality ? existingWorkData.high_quality.length : 0})，file_id: '${existingWorkData.file_id}' (长度: ${existingWorkData.file_id ? existingWorkData.file_id.length : 0})`);
          
          if (existingWorkData.high_quality && existingWorkData.high_quality.trim() !== "" && existingWorkData.high_quality.trim() !== "done") {
            logger.info(`[FILE_ID_CHECK] 作品 ${awemeId} 使用 high_quality 字段: ${existingWorkData.high_quality}`);
            fileIdsToUseForSend = existingWorkData.high_quality.split(';').filter(id => id);
            sourceOfFileIdsInfo = "high_quality";
            logger.info(`[FILE_ID_CHECK] high_quality 解析结果: ${JSON.stringify(fileIdsToUseForSend)}`);
          } else if (existingWorkData.file_id && existingWorkData.file_id.trim() !== "") {
            logger.info(`[FILE_ID_CHECK] 作品 ${awemeId} 使用 file_id 字段: ${existingWorkData.file_id}`);
            fileIdsToUseForSend = existingWorkData.file_id.split(';').filter(id => id);
            sourceOfFileIdsInfo = "file_id";
            logger.info(`[FILE_ID_CHECK] file_id 解析结果: ${JSON.stringify(fileIdsToUseForSend)}`);
          } else {
            logger.warning(`[FILE_ID_CHECK] 作品 ${awemeId} 的 high_quality 和 file_id 字段都为空或无效`);
          }

          if (fileIdsToUseForSend.length > 0) {
            logger.info(`[DB_SEND] 作品 ${awemeId} 将使用来自 ${sourceOfFileIdsInfo} 字段的 file_id 列表发送，文件数量: ${fileIdsToUseForSend.length}，文件列表: ${JSON.stringify(fileIdsToUseForSend)}`);
            const authorData = existingWorkData.douyin_user;
            if (authorData && authorData.sec_uid) {
              // 不再调用任何 fetchUserProfileApi 之类的
            }
            
            const dbParsedData = {
              base_info: {
                aweme_id: existingWorkData.aweme_id,
                desc: existingWorkData.description,
                create_time: existingWorkData.create_time
              },
              author_info: {
                nickname: existingWorkData.nickname,
                uid: existingWorkData.uid,
                sec_uid: existingWorkData.sec_uid,
                unique_id: existingWorkData.unique_id,
                follower_count: existingWorkData.follower_count,
                total_favorited: existingWorkData.total_favorited
              },
              location_info: {
                province: existingWorkData.province,
                city: existingWorkData.city
              },
              music_info: {
                title: existingWorkData.music_title,
                author: existingWorkData.music_author,
                play_url: existingWorkData.music_play_url
              }
            };
            
            const captionText = buildCaptionForSingle(dbParsedData);
            const secUid = existingWorkData.sec_uid || "";
            
            const fileIds = fileIdsToUseForSend; // Use the resolved file IDs
            let zipFileId = null;
            let audioFileId = null;
            for (const fileId of fileIds) { // Iterate over resolved file IDs
              if (detectFileType(fileId) === 'document') zipFileId = fileId;
              if (detectFileType(fileId) === 'audio') audioFileId = fileId;
            }

            const buttons = [];
            buttons.push(Markup.button.url("打开", douyinUrl));
            buttons.push(Markup.button.callback("获取音乐", `music:${awemeId}`));
            if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${awemeId}`));
            
            // 检查用户是否已订阅该作者的作品和直播
            if (existingWorkData.uid) {
                let isSubscribedWork = false;
                let isSubscribedLive = false;
                try {
                    isSubscribedWork = await isUserSubscribedWork(chatId, existingWorkData.uid);
                    isSubscribedLive = await isUserSubscribedLive(chatId, existingWorkData.uid);
                } catch (e) { 
                    logger.error("Error checking subscription status in text handler (existing work)", e);
                }

                // 根据作品/直播订阅状态添加按钮
                if (isSubscribedWork) {
                    buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${existingWorkData.uid}:${awemeId}`));
                } else {
                    buttons.push(Markup.button.callback("订阅作品", `sub:${existingWorkData.uid}`));
                }

                // 只有在启用了直播功能时才显示直播订阅按钮
                if (SHOW_LIVE_BUTTONS) {
                    if (isSubscribedLive) {
                        buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${existingWorkData.uid}`));
                    } else {
                        buttons.push(Markup.button.callback("订阅直播", `sub_live:${existingWorkData.uid}`));
                    }
                }
            } else if (secUid) {
                logger.warning(`bot.on(text): existingWorkData for ${awemeId} has secUid but no uid. Cannot create sub button.`);
            }
            const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));

            await sendMediaByFileIds(ctx, fileIds, captionText, markup, awemeId, true); // skipSentUpdate = true 低危操作
            await updateUser(chatId, { already: alreadyUsed + 1 });
            await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
            cleanupMessages(ctx, processingMsg);
            return; // Important: return after sending
          } else {
            logger.warning(`[DB_SEND] 作品 ${awemeId} 存在于数据库，但 high_quality 和 file_id 均为空或无效。数据库记录详情: high_quality='${existingWorkData.high_quality}', file_id='${existingWorkData.file_id}', 记录ID=${existingWorkData.id}。将进行完整API处理。`);
            // No 'return' here, so it falls through to subsequent API call logic.
          }
        }

        // 如果没有aweme_id，需要先通过API获取
        let respData;
        if (!awemeId) {
          logger.info(`[API_CALL] 无aweme_id，需要先调用API获取，URL: ${douyinUrl}`);
          
          let apiResp;
          try {
            apiResp = await callHybridVideoData(douyinUrl);
          } catch (apiError) {
            logger.error(`[API_CALL] API调用失败: ${apiError.message}`, apiError);
            await ctx.telegram.editMessageText(
              chatId,
              processingMsg.message_id,
              null,
              "抱歉，无法解析该链接，请检查链接是否有效。"
            );
            await updateUser(chatId, { already: alreadyUsed + 1 });
            cleanupMessages(ctx, processingMsg);
            return;
          }
          
          // 尝试从API响应中获取aweme_id
          if (apiResp && apiResp.data && apiResp.data.aweme_id) {
            awemeId = apiResp.data.aweme_id;
            logger.info(`[API_CALL] 从API获取到aweme_id: ${awemeId}，现在查询数据库`);
            
            // 重新查询数据库
            const { data: existingData, error: checkError } = await supabase
              .from("douyin")
              .select("*")
              .eq("aweme_id", awemeId)
              .limit(1);
            
            if (!checkError && existingData && existingData.length > 0) {
              existingWorkData = existingData[0];
              isWorkExistInDB = true;
              logger.info(`[DB_QUERY_AFTER_API] 作品 ${awemeId} 已存在于数据库，记录ID: ${existingWorkData.id}，file_id: ${existingWorkData.file_id ? '有数据' : '空'}，high_quality: ${existingWorkData.high_quality ? '有数据' : '空'}`);
              
              // 检查是否有可用的file_id
              let fileIdsToUseForSend = [];
              if (existingWorkData.high_quality && existingWorkData.high_quality.trim() !== "" && existingWorkData.high_quality.trim() !== "done") {
                fileIdsToUseForSend = existingWorkData.high_quality.split(';').filter(id => id);
              } else if (existingWorkData.file_id && existingWorkData.file_id.trim() !== "") {
                fileIdsToUseForSend = existingWorkData.file_id.split(';').filter(id => id);
              }
              
              // 如果有file_id，直接发送
              if (fileIdsToUseForSend.length > 0) {
                logger.info(`[DB_SEND_AFTER_API] 作品 ${awemeId} 使用数据库中的file_id直接发送`);
                
                const dbParsedData = {
                  base_info: {
                    aweme_id: existingWorkData.aweme_id,
                    desc: existingWorkData.description,
                    create_time: existingWorkData.create_time
                  },
                  author_info: {
                    nickname: existingWorkData.nickname,
                    uid: existingWorkData.uid,
                    sec_uid: existingWorkData.sec_uid,
                    unique_id: existingWorkData.unique_id,
                    follower_count: existingWorkData.follower_count,
                    total_favorited: existingWorkData.total_favorited
                  },
                  location_info: {
                    province: existingWorkData.province,
                    city: existingWorkData.city
                  },
                  music_info: {
                    title: existingWorkData.music_title,
                    author: existingWorkData.music_author,
                    play_url: existingWorkData.music_play_url
                  }
                };
                
                const captionText = buildCaptionForSingle(dbParsedData);
                const fileIds = fileIdsToUseForSend;
                
                let zipFileId = null;
                let audioFileId = null;
                for (const fileId of fileIds) {
                  if (detectFileType(fileId) === 'document') zipFileId = fileId;
                  if (detectFileType(fileId) === 'audio') audioFileId = fileId;
                }
                
                const buttons = [];
                buttons.push(Markup.button.url("打开", douyinUrl));
                buttons.push(Markup.button.callback("获取音乐", `music:${awemeId}`));
                if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${awemeId}`));
                
                if (existingWorkData.uid) {
                  let isSubscribedWork = false;
                  let isSubscribedLive = false;
                  try {
                    isSubscribedWork = await isUserSubscribedWork(chatId, existingWorkData.uid);
                    isSubscribedLive = await isUserSubscribedLive(chatId, existingWorkData.uid);
                  } catch (e) { 
                    logger.error("Error checking subscription status after API", e);
                  }
                  
                  if (isSubscribedWork) {
                    buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${existingWorkData.uid}:${awemeId}`));
                  } else {
                    buttons.push(Markup.button.callback("订阅作品", `sub:${existingWorkData.uid}`));
                  }
                  
                  if (SHOW_LIVE_BUTTONS) {
                    if (isSubscribedLive) {
                      buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${existingWorkData.uid}`));
                    } else {
                      buttons.push(Markup.button.callback("订阅直播", `sub_live:${existingWorkData.uid}`));
                    }
                  }
                }
                
                const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));
                await sendMediaByFileIds(ctx, fileIds, captionText, markup, awemeId, true);
                await updateUser(chatId, { already: alreadyUsed + 1 });
                await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
                cleanupMessages(ctx, processingMsg);
                return;
              }
            }
          } else {
            // API返回成功但没有aweme_id
            logger.error(`[API_CALL] API返回数据中没有aweme_id，URL: ${douyinUrl}`);
            await ctx.telegram.editMessageText(
              chatId,
              processingMsg.message_id,
              null,
              "抱歉，无法解析该链接，请检查链接是否有效。"
            );
            await updateUser(chatId, { already: alreadyUsed + 1 });
            cleanupMessages(ctx, processingMsg);
            return;
          }
          
          // 如果数据库中没有可用的file_id，继续处理API返回的数据
          respData = apiResp;
        } else {
          // 如果已经有aweme_id但数据库中没有或没有file_id，需要调用API获取完整信息
          logger.info(`[API_CALL] 准备调用API，原因: ${isWorkExistInDB ? '数据库记录存在但file_id为空' : '数据库无记录'}，aweme_id: ${awemeId}，isStandardVideoUrl: ${isStandardVideoUrl}`);
          try {
            respData = await callHybridVideoData(douyinUrl);
          } catch (apiError) {
            logger.error(`[API_CALL] API调用失败: ${apiError.message}`, apiError);
            await ctx.telegram.editMessageText(
              chatId,
              processingMsg.message_id,
              null,
              "抱歉，无法获取视频信息，请稍后重试。"
            );
            await updateUser(chatId, { already: alreadyUsed + 1 });
            cleanupMessages(ctx, processingMsg);
            return;
          }
        }


        if (respData) {
          
          // 检查是否需要静默处理
          if (respData.silent_process) {
            logger.info(`检测到需要静默处理的链接: ${douyinUrl}`);
            
            // 静默删除用户消息，不给任何提示
            try {
              await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id);
              
              // 如果有处理中的消息，也静默删除
              if (processingMsg && processingMsg.message_id) {
                await ctx.telegram.deleteMessage(ctx.chat.id, processingMsg.message_id);
              }
              
              logger.info(`已静默删除用户 ${chatId} 发送的不支持链接: ${douyinUrl}`);
            } catch (deleteError) {
              logger.error(`静默删除消息失败: ${deleteError.message}`, deleteError);
            }
            
            return;
          }
          
          // 原有的not_available处理逻辑
          if (respData.not_available) {
            const respAwemeId = respData.data?.aweme_id || awemeId;
            let notAvailableMessage = `抱歉，该作品${respAwemeId ? ` (ID: ${respAwemeId})` : ""}不可用或已被删除。\n\n已在系统中标记该作品状态。`;
            
            // 检查是否为长视频
            if (respData.is_long_video) {
              notAvailableMessage = `抱歉，检测到这是一个长视频内容${respAwemeId ? ` (ID: ${respAwemeId})` : ""}，暂不支持下载。\n\n已在系统中记录此作品信息。`;
            }
            
            await ctx.telegram.editMessageText(
              chatId, 
              processingMsg.message_id, 
              null, 
              notAvailableMessage
            );
            await updateUser(chatId, { already: alreadyUsed + 1 });
            cleanupMessages(ctx, processingMsg);
            return;
          }
        }

        if (respData?.data?.aweme_id) {
          const apiAwemeId = respData.data.aweme_id;
          if (apiAwemeId !== awemeId) {
            const { data: existingApiData, error: apiCheckError } = await supabase
              .from("douyin")
              .select("*")
              .eq("aweme_id", apiAwemeId)
              .limit(1);
            
            if (!apiCheckError && existingApiData && existingApiData.length > 0) {
              isWorkExistInDB = true;
              const existingRecord = existingApiData[0];

              let fileIdsToUseFromApiRecord = [];
              let sourceInfo = "";
              if (existingRecord.high_quality && existingRecord.high_quality.trim() !== "" && existingRecord.high_quality.trim() !== "done") {
                logger.info(`API解析的作品 ${apiAwemeId} (DB record) 使用 high_quality: ${existingRecord.high_quality}`);
                fileIdsToUseFromApiRecord = existingRecord.high_quality.split(';').filter(id => id);
                sourceInfo = "high_quality";
              } else if (existingRecord.file_id && existingRecord.file_id.trim() !== "") {
                logger.info(`API解析的作品 ${apiAwemeId} (DB record) 使用 file_id: ${existingRecord.file_id}`);
                fileIdsToUseFromApiRecord = existingRecord.file_id.split(';').filter(id => id);
                sourceInfo = "file_id";
              }

              if (fileIdsToUseFromApiRecord.length > 0) {
                logger.info(`API解析的作品 ${apiAwemeId} (DB record) 将使用来自 ${sourceInfo} 字段的 file_id 发送。`);
                
                // 确保作者信息存在于douyin_user表中
                if (existingRecord.uid) {
                  const userData = {
                    uid: existingRecord.uid,
                    sec_uid: existingRecord.sec_uid || null,
                    nickname: existingRecord.nickname || null,
                    unique_id: existingRecord.unique_id || null,
                    follower_count: existingRecord.follower_count || 0,
                    total_favorited: existingRecord.total_favorited || 0
                  };
                  await saveDouyinUserToDatabase(userData);
                }
                
                const dbParsedData = {
                  base_info: {
                    aweme_id: existingRecord.aweme_id,
                    desc: existingRecord.description,
                    create_time: existingRecord.create_time
                  },
                  author_info: {
                    nickname: existingRecord.nickname,
                    uid: existingRecord.uid,
                    sec_uid: existingRecord.sec_uid,
                    unique_id: existingRecord.unique_id,
                    follower_count: existingRecord.follower_count,
                    total_favorited: existingRecord.total_favorited
                  },
                  location_info: {
                    province: existingRecord.province,
                    city: existingRecord.city
                  },
                  music_info: {
                    title: existingRecord.music_title,
                    author: existingRecord.music_author,
                    play_url: existingRecord.music_play_url
                  }
                };
                const captionText = buildCaptionForSingle(dbParsedData);
                const secUid = existingRecord.sec_uid || "";
                
                const fileIds = fileIdsToUseFromApiRecord; // Use these
                let zipFileId = null;
                let audioFileId = null;
                for (const fileId of fileIds) {
                  if (detectFileType(fileId) === 'document') zipFileId = fileId;
                  if (detectFileType(fileId) === 'audio') audioFileId = fileId;
                }
                
                const buttons = [];
                buttons.push(Markup.button.url("打开", douyinUrl));
                buttons.push(Markup.button.callback("获取音乐", `music:${apiAwemeId}`));
                if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${apiAwemeId}`));
                
                // Use UID for subscribe button
                if (existingRecord.uid) {
                    let isSubscribedWork = false;
                    let isSubscribedLive = false;
                    try {
                        isSubscribedWork = await isUserSubscribedWork(chatId, existingRecord.uid);
                        isSubscribedLive = await isUserSubscribedLive(chatId, existingRecord.uid);
                    } catch (e) { logger.error("Error checking subscription status in text handler (API existing work)", e);}
                    
                    if (isSubscribedWork) {
                        buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${existingRecord.uid}:${apiAwemeId}`));
                    } else {
                        buttons.push(Markup.button.callback("订阅作品", `sub:${existingRecord.uid}`));
                    }
                    
                    // 只有在启用了直播功能时才显示直播订阅按钮
                    if (SHOW_LIVE_BUTTONS) {
                        if (isSubscribedLive) {
                            buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${existingRecord.uid}`));
                        } else {
                            buttons.push(Markup.button.callback("订阅直播", `sub_live:${existingRecord.uid}`));
                        }
                    }
                } else if (secUid) { // secUid here is existingRecord.sec_uid
                     logger.warning(`bot.on(text): existingRecord for ${apiAwemeId} (from API) has secUid but no uid. Cannot create sub button.`);
                }
                const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));

                await sendMediaByFileIds(ctx, fileIds, captionText, markup, apiAwemeId, true); // skipSentUpdate = true 低危操作
                await updateUser(chatId, { already: alreadyUsed + 1 });
                await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
                cleanupMessages(ctx, processingMsg);
                return;
              }
              // If no usable file_ids, fall through
            }
          }
        }

        let parsedData = parseDouyinWork(respData);
        if (!parsedData || !parsedData.base_info?.aweme_id) {
          if (isStandardVideoUrl && awemeId) {
            logger.info(`从最小化响应解析失败，尝试直接调用API获取作品 ${awemeId} 详情`);
            respData = await callHybridVideoData(douyinUrl);
            if (respData.not_available) {
              await ctx.telegram.editMessageText(
                chatId, 
                processingMsg.message_id, 
                null, 
                `抱歉，该作品 (ID: ${awemeId}) 不可用或已被删除。\n\n已在系统中标记该作品状态。`
              );
              await updateUser(chatId, { already: alreadyUsed + 1 });
              cleanupMessages(ctx, processingMsg);
              return;
            }
            
            const apiAwemeId = respData?.data?.aweme_id;
            if (apiAwemeId) {
              const { data: existingApiData, error: apiCheckError } = await supabase
                .from("douyin")
                .select("*")
                .eq("aweme_id", apiAwemeId)
                .limit(1);
              if (!apiCheckError && existingApiData && existingApiData.length > 0) {
                isWorkExistInDB = true;
                const existingRecord = existingApiData[0];
                
                // 确保作者信息存在于douyin_user表中
                if (existingRecord.uid) {
                  const userData = {
                    uid: existingRecord.uid,
                    sec_uid: existingRecord.sec_uid || null,
                    nickname: existingRecord.nickname || null,
                    unique_id: existingRecord.unique_id || null,
                    follower_count: existingRecord.follower_count || 0,
                    total_favorited: existingRecord.total_favorited || 0
                  };
                  await saveDouyinUserToDatabase(userData);
                }
                
                if (existingRecord.file_id) {
                  const dbParsedData = {
                    base_info: {
                      aweme_id: existingRecord.aweme_id,
                      desc: existingRecord.description,
                      create_time: existingRecord.create_time
                    },
                    author_info: {
                      nickname: existingRecord.nickname,
                      uid: existingRecord.uid,
                      sec_uid: existingRecord.sec_uid,
                      unique_id: existingRecord.unique_id,
                      follower_count: existingRecord.follower_count,
                      total_favorited: existingRecord.total_favorited
                    },
                    location_info: {
                      province: existingRecord.province,
                      city: existingRecord.city
                    },
                    music_info: {
                      title: existingRecord.music_title,
                      author: existingRecord.music_author,
                      play_url: existingRecord.music_play_url
                    }
                  };
                  const captionText = buildCaptionForSingle(dbParsedData);
                  const secUid = existingRecord.sec_uid || "";
                  
                  const fileIds = existingRecord.file_id.split(';').filter(id => id);
                  let zipFileId = null;
                  let audioFileId = null;
                  for (const fileId of fileIds) {
                    if (detectFileType(fileId) === 'document') zipFileId = fileId;
                    if (detectFileType(fileId) === 'audio') audioFileId = fileId;
                  }
                  
                  const buttons = [];
                  buttons.push(Markup.button.url("打开", douyinUrl));
                  buttons.push(Markup.button.callback("获取音乐", `music:${apiAwemeId}`));
                  if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${apiAwemeId}`));
                  
                  // Use UID for subscribe button
                  if (existingRecord.uid) {
                        let isSubscribedWork = false;
                        let isSubscribedLive = false;
                        try {
                            isSubscribedWork = await isUserSubscribedWork(chatId, existingRecord.uid);
                            isSubscribedLive = await isUserSubscribedLive(chatId, existingRecord.uid);
                        } catch (e) { logger.error("Error checking subscription status in text handler (API fallback existing work)", e);}
                        
                        if (isSubscribedWork) {
                            buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${existingRecord.uid}:${apiAwemeId}`));
                        } else {
                            buttons.push(Markup.button.callback("订阅作品", `sub:${existingRecord.uid}`));
                        }
                        
                        // 只有在启用了直播功能时才显示直播订阅按钮
                        if (SHOW_LIVE_BUTTONS) {
                            if (isSubscribedLive) {
                                buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${existingRecord.uid}`));
                            } else {
                                buttons.push(Markup.button.callback("订阅直播", `sub_live:${existingRecord.uid}`));
                            }
                        }
                  } else if (secUid) { // secUid here is existingRecord.sec_uid
                        logger.warning(`bot.on(text): existingRecord for ${apiAwemeId} (from API) has secUid but no uid. Cannot create sub button.`);
                  }
                  const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));

                  await sendMediaByFileIds(ctx, fileIds, captionText, markup, apiAwemeId, true); // skipSentUpdate = true 低危操作
                  await updateUser(chatId, { already: alreadyUsed + 1 });
                  await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
                  cleanupMessages(ctx, processingMsg);
                  return;
                }
              }
            }
            parsedData = parseDouyinWork(respData);
            if (!parsedData || !parsedData.base_info?.aweme_id) {
              await ctx.telegram.editMessageText(
                chatId, 
                processingMsg.message_id, 
                null, 
                "解析失败，链接无效或视频无法获取。"
              );
              if (awemeId) {
                try {
                  const minimalData = {
                    base_info: { aweme_id: awemeId, desc: "Not Available" },
                    author_info: {}, 
                    location_info: {}, 
                    music_info: {}, 
                    media_info: {}
                  };
                  await saveDouyinToDatabase(minimalData);
                  logger.info(`因API解析失败，已将作品 ${awemeId} 标记为不可用`);
                } catch (error) {
                  logger.error(`尝试标记不可用作品 ${awemeId} 时出错: ${error.message}`, error);
                }
              }
              cleanupMessages(ctx, processingMsg);
              return;
            }
          } else {
            await ctx.telegram.editMessageText(
              chatId, 
              processingMsg.message_id, 
              null, 
              "解析失败，链接无效或视频无法获取。"
            );
            if (awemeId) {
              try {
                const minimalData = {
                  base_info: { aweme_id: awemeId, desc: "Not Available" },
                  author_info: {}, 
                  location_info: {}, 
                  music_info: {}, 
                  media_info: {}
                };
                await saveDouyinToDatabase(minimalData);
                logger.info(`因解析失败，已将作品 ${awemeId} 标记为不可用`);
              } catch (error) {
                logger.error(`尝试标记不可用作品 ${awemeId} 时出错: ${error.message}`, error);
              }
            }
            cleanupMessages(ctx, processingMsg);
            return;
          }
        }

        const finalAwemeId = parsedData.base_info.aweme_id;
        logger.info(`成功解析作品信息，aweme_id: ${finalAwemeId}`);

        if (!isWorkExistInDB && finalAwemeId) {
          const { data: finalCheck, error: finalCheckError } = await supabase
            .from("douyin")
            .select("*")
            .eq("aweme_id", finalAwemeId)
            .limit(1);
          if (!finalCheckError && finalCheck && finalCheck.length > 0) {
            isWorkExistInDB = true;
            const existingRecord = finalCheck[0];
            logger.info(`最终解析的作品 ${finalAwemeId} 已存在于数据库`);

            let fileIdsToUseFromFinalCheck = [];
            let sourceInfo = "";
            if (existingRecord.high_quality && existingRecord.high_quality.trim() !== "" && existingRecord.high_quality.trim() !== "done") {
              logger.info(`作品 ${finalAwemeId} (DB final check) 使用 high_quality: ${existingRecord.high_quality}`);
              fileIdsToUseFromFinalCheck = existingRecord.high_quality.split(';').filter(id => id);
              sourceInfo = "high_quality";
            } else if (existingRecord.file_id && existingRecord.file_id.trim() !== "") {
              logger.info(`作品 ${finalAwemeId} (DB final check) 使用 file_id: ${existingRecord.file_id}`);
              fileIdsToUseFromFinalCheck = existingRecord.file_id.split(';').filter(id => id);
              sourceInfo = "file_id";
            }
            
            if (fileIdsToUseFromFinalCheck.length > 0) {
              logger.info(`作品 ${finalAwemeId} 已有可用的 ${sourceInfo}，将直接使用现有记录发送`);
              
              // 即使作品已存在，也要确保作者信息在douyin_user表中
              if (existingRecord.uid) {
                const userData = {
                  uid: existingRecord.uid,
                  sec_uid: existingRecord.sec_uid || null,
                  nickname: existingRecord.nickname || null,
                  unique_id: existingRecord.unique_id || null,
                  follower_count: existingRecord.follower_count || 0,
                  total_favorited: existingRecord.total_favorited || 0
                };
                await saveDouyinUserToDatabase(userData);
              }
              
              const dbParsedData = {
                base_info: {
                  aweme_id: existingRecord.aweme_id,
                  desc: existingRecord.description,
                  create_time: existingRecord.create_time
                },
                author_info: {
                  nickname: existingRecord.nickname,
                  uid: existingRecord.uid,
                  sec_uid: existingRecord.sec_uid,
                  unique_id: existingRecord.unique_id,
                  follower_count: existingRecord.follower_count,
                  total_favorited: existingRecord.total_favorited
                },
                location_info: {
                  province: existingRecord.province,
                  city: existingRecord.city
                },
                music_info: {
                  title: existingRecord.music_title,
                  author: existingRecord.music_author,
                  play_url: existingRecord.music_play_url
                }
              };
              const captionText = buildCaptionForSingle(dbParsedData);
              const secUid = existingRecord.sec_uid || "";
              
              const fileIds = fileIdsToUseFromFinalCheck; // Use these
              let zipFileId = null;
              let audioFileId = null;
              for (const fileId of fileIds) {
                if (detectFileType(fileId) === 'document') zipFileId = fileId;
                if (detectFileType(fileId) === 'audio') audioFileId = fileId;
              }
              
              const buttons = [];
              buttons.push(Markup.button.url("打开", douyinUrl));
              buttons.push(Markup.button.callback("获取音乐", `music:${finalAwemeId}`));
              if (zipFileId) buttons.push(Markup.button.callback("获取文件", `zip:${finalAwemeId}`));
              
              // Use UID for subscribe button
              if (existingRecord.uid) {
                    let isSubscribedWork = false;
                    let isSubscribedLive = false;
                    try {
                        isSubscribedWork = await isUserSubscribedWork(chatId, existingRecord.uid);
                        isSubscribedLive = await isUserSubscribedLive(chatId, existingRecord.uid);
                    } catch (e) { logger.error("Error checking subscription status in text handler (API existing work)", e);}
                    
                    if (isSubscribedWork) {
                        buttons.push(Markup.button.callback("取消订阅作品", `work_unsub:${existingRecord.uid}:${apiAwemeId}`));
                    } else {
                        buttons.push(Markup.button.callback("订阅作品", `sub:${existingRecord.uid}`));
                    }
                    
                    // 只有在启用了直播功能时才显示直播订阅按钮
                    if (SHOW_LIVE_BUTTONS) {
                        if (isSubscribedLive) {
                            buttons.push(Markup.button.callback("取消订阅直播", `unsub_live:${existingRecord.uid}`));
                        } else {
                            buttons.push(Markup.button.callback("订阅直播", `sub_live:${existingRecord.uid}`));
                        }
                    }
              } else if (secUid) { // secUid here is existingRecord.sec_uid
                    logger.warning(`bot.on(text): existingRecord for ${finalAwemeId} (from API) has secUid but no uid. Cannot create sub button.`);
              }
              const markup = Markup.inlineKeyboard(smartButtonLayout(buttons));

              await sendMediaByFileIds(ctx, fileIds, captionText, markup, finalAwemeId, true); // skipSentUpdate = true 低危操作
              await updateUser(chatId, { already: alreadyUsed + 1 });
              await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理完成。");
              cleanupMessages(ctx, processingMsg);
              return;
            } else {
              logger.info(`作品 ${finalAwemeId} 存在于数据库 (final check) 但 high_quality 和 file_id 均为空或无效，将进行完整API后续处理。`);
            }
          }
        }

        if (!isWorkExistInDB) {
          // 检查视频时长（仅在创建新记录时）
          if (respData.video_duration_seconds && respData.video_duration_seconds > 600) {
            logger.info(`检测到超长视频 (${respData.video_duration_seconds.toFixed(2)}秒 > 10分钟): ${finalAwemeId}，将标记为failed`);
            
            // 保存作品信息到数据库
            logger.info(`将作品 ${finalAwemeId} 保存到数据库`);
            await saveDouyinToDatabase(parsedData);
            logger.info(`成功保存作品 ${finalAwemeId} 到数据库`);
            
            // 标记为failed
            const failedReason = "video_too_long:视频时长超过10分钟";
            const { error: failedUpdateError } = await supabase
              .from("douyin")
              .update({ failed: failedReason })
              .eq("aweme_id", finalAwemeId);
            
            if (failedUpdateError) {
              logger.error(`更新超长视频 ${finalAwemeId} 的failed字段失败: ${failedUpdateError.message}`, failedUpdateError);
            } else {
              logger.info(`已将超长视频 ${finalAwemeId} 的failed字段更新为 ${failedReason}`);
            }
            
            // 向用户返回错误信息
            await ctx.telegram.editMessageText(
              chatId,
              processingMsg.message_id,
              null,
              "视频时长超过10分钟，文件过大无法处理。"
            );
            await updateUser(chatId, { already: alreadyUsed + 1 });
            cleanupMessages(ctx, processingMsg);
            return;
          }
          
          logger.info(`将作品 ${finalAwemeId} 保存到数据库`);
          await saveDouyinToDatabase(parsedData);
          logger.info(`成功保存作品 ${finalAwemeId} 到数据库`);
        } else {
          logger.info(`作品 ${finalAwemeId} 已存在于数据库，跳过保存`);
        }
        
        // 同时保存作者信息到douyin_user表
        const authorInfo = parsedData?.author_info || {};
        if (authorInfo.uid && authorInfo.sec_uid) {
          const userData = {
            uid: authorInfo.uid,
            sec_uid: authorInfo.sec_uid,
            nickname: authorInfo.nickname || null,
            unique_id: authorInfo.unique_id || null,
            follower_count: authorInfo.follower_count || 0,
            total_favorited: authorInfo.total_favorited || 0
          };
          await saveDouyinUserToDatabase(userData);
        }

        // 直接标记所有新作品为 large，交给其他程序处理
        logger.info(`将作品 ${finalAwemeId} 标记为 large，交给后台处理`);
        await updateLargeField(ctx, finalAwemeId, "新作品统一后台处理");
        await updateUser(chatId, { already: alreadyUsed + 1 });
        
        // 延迟10秒后删除消息（使用默认值）
        cleanupMessages(ctx, processingMsg);
        return;
      } catch (error) {
        logger.error(`处理抖音链接时出错: ${error.message}`, error);
        await ctx.telegram.editMessageText(chatId, processingMsg.message_id, null, "处理时发生错误，请稍后再试。");
        cleanupMessages(ctx, processingMsg);
        return;
      }
    }

    const replyMsg = await ctx.reply("未检测到有效的抖音链接。");
    setTimeout(async () => {
      try { await ctx.telegram.deleteMessage(ctx.chat.id, replyMsg.message_id); } catch (e) {}
      try { await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id); } catch (e) {}
    }, 10000);
  } catch (error) { // 这是 bot.on('text', ...) 的最外层 catch
    logger.error(`处理文本消息出错: ${error.message}`, error);
    if (ctx && ctx.reply) {
      // 为了避免双重错误消息(如果内部catch已处理并静默删除)，此处不再主动回复
      // await ctx.reply(`❌ 出错: ${error.message}`);
    }
  }
});

function cleanupMessages(ctx, processMsg, delay = 10.0) {
  // 使用 setTimeout 而不是 await，避免阻塞
  setTimeout(async () => {
    // 如果processMsg为null，只删除用户消息
    if (processMsg) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, processMsg.message_id);
      } catch (error) {
        logger.debug(`删除处理消息失败: ${error.message}`);
      }
    }
    try {
      await ctx.telegram.deleteMessage(ctx.chat.id, ctx.message.message_id);
    } catch (error) {
      logger.debug(`删除用户消息失败: ${error.message}`);
    }
  }, delay * 1000);
}

// =============== 标记作品为失败状态 ===============
async function markWorkAsFailed(awemeId, chatId) {
  if (!awemeId) {
    logger.error(`无法标记失败状态：缺少 awemeId`);
    return;
  }
  if (!chatId) {
    logger.error(`无法标记失败状态 (awemeId: ${awemeId})：缺少 chatId`);
    return;
  }
  try {
    const failedStatus = `file id pending:${chatId}`;
    const { error } = await supabase
      .from("douyin")
      .update({ failed: failedStatus })
      .eq("aweme_id", awemeId);

    if (error) {
      logger.error(`标记作品 ${awemeId} 为失败状态 (${failedStatus}) 失败: ${error.message}`, error);
    } else {
      logger.info(`已将作品 ${awemeId} 标记为失败状态 (${failedStatus})`);
    }
  } catch (dbError) {
    logger.error(`标记作品 ${awemeId} 为失败状态时发生异常: ${dbError.message}`, dbError);
  }
}


// 混合数据接口 - 保留，主要用来获取作品信息
async function callHybridVideoData(douyinUrl) {
  return retryApiCallWithBackoff(async () => {
    try {
      const encodedUrl = encodeURIComponent(douyinUrl);
      
      logger.info(`[callHybridVideoData] 原始URL: ${douyinUrl}`);
      
      // 使用端口故障转移
      const { response, url: actualUrl } = await callWithPortFailover(
        (baseUrl) => `${baseUrl}/api/hybrid/video_data?url=${encodedUrl}&minimal=false`,
        { timeout: 30000 }
      );
      
      logger.info(`[callHybridVideoData] 完整API URL: ${actualUrl}`);
      logger.info(`[callHybridVideoData] 状态码: ${response.status}`);
      
      if (response.status === 429) {
        throw { status: 429, message: "API限流，请求过于频繁" };
      }
      
      const text = await response.text();
      logger.info(`[callHybridVideoData] 响应内容(前200字): ${text.substring(0, 200)}...`);
      
      if (!response.ok) {
        // 主API请求失败，尝试使用TikHub API作为备用方案
        logger.info(`[callHybridVideoData] 主API调用失败，尝试使用TikHub API作为备用方案`);
        try {
          // 检查是否有TikHub API密钥
          const tikhubApiKey = process.env.TIKHUB_API_KEY;
          if (!tikhubApiKey) {
            logger.warning(`[callHybridVideoData] 未配置TIKHUB_API_KEY环境变量，无法使用备用API`);
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const tikhubUrl = `https://api.tikhub.io/api/v1/douyin/app/v3/fetch_one_video_by_share_url?share_url=${encodedUrl}`;
          
          logger.info(`[callHybridVideoData] 尝试TikHub API: ${tikhubUrl}`);
          
          const tikhubResponse = await fetch(tikhubUrl, {
            method: 'GET',
            headers: {
              'accept': 'application/json',
              'Authorization': `Bearer ${tikhubApiKey}`
            },
            timeout: 30000
          });
          
          if (!tikhubResponse.ok) {
            logger.error(`[callHybridVideoData] TikHub API请求失败: ${tikhubResponse.status}`);
            throw new Error(`TikHub API HTTP error! status: ${tikhubResponse.status}`);
          }
          
          const tikhubData = await tikhubResponse.json();
          logger.info(`[callHybridVideoData] TikHub API响应成功`);
          
          // 检查是否包含aweme_id
          let awemeId = null;
          if (tikhubData.data && tikhubData.data.filter_list && tikhubData.data.filter_list.length > 0) {
            awemeId = tikhubData.data.filter_list[0].aweme_id;
            logger.info(`[callHybridVideoData] 从TikHub API获取到aweme_id: ${awemeId}`);
          } else if (tikhubData.data && tikhubData.data.aweme_details && tikhubData.data.aweme_details.length > 0) {
            awemeId = tikhubData.data.aweme_details[0].aweme_id;
            logger.info(`[callHybridVideoData] 从TikHub API的aweme_details获取到aweme_id: ${awemeId}`);
          }
          
          if (awemeId) {
            // 标记为不可用，但记录aweme_id
            logger.info(`[callHybridVideoData] 长视频检测到aweme_id: ${awemeId}，将静默处理并标记为not available`);
            
            // 调用本地API获取filter_reason
            try {
              logger.info(`[callHybridVideoData] 调用本地API获取filter_reason`);
              
              // 使用端口故障转移
              const { response: localApiResponse, url: localApiUrl } = await callWithPortFailover(
                (baseUrl) => `${baseUrl}/api/douyin/web/fetch_one_video?aweme_id=${awemeId}`,
                { timeout: 30000 }
              );
              
              logger.info(`[callHybridVideoData] 本地API URL: ${localApiUrl}`);
              if (localApiResponse.ok) {
                const localApiData = await localApiResponse.json();
                logger.info(`[callHybridVideoData] 本地API响应成功`);
                
                let filterReason = "unknown";
                if (localApiData.data && 
                    localApiData.data.filter_detail && 
                    localApiData.data.filter_detail.filter_reason) {
                  filterReason = localApiData.data.filter_detail.filter_reason;
                  logger.info(`[callHybridVideoData] 获取到filter_reason: ${filterReason}`);
                }
                
                // 构建最小化数据对象，包含filter_reason
                const minimalData = {
                  base_info: { 
                    aweme_id: awemeId, 
                    desc: filterReason  // 将filter_reason作为描述
                  },
                  author_info: {},
                  location_info: {},
                  music_info: {},
                  media_info: {}
                };
                
                // 将数据保存到数据库
                await saveDouyinToDatabase(minimalData);
              } else {
                logger.warning(`[callHybridVideoData] 本地API请求失败: ${localApiResponse.status}`);
                // 如果本地API调用失败，仍然保存基本信息
                const minimalData = {
                  base_info: { 
                    aweme_id: awemeId, 
                    desc: "Not Available (Long Video)"
                  },
                  author_info: {},
                  location_info: {},
                  music_info: {},
                  media_info: {}
                };
                await saveDouyinToDatabase(minimalData);
              }
            } catch (localApiError) {
              logger.error(`[callHybridVideoData] 本地API调用错误: ${localApiError.message}`, localApiError);
              // 出错时仍然保存基本信息
              const minimalData = {
                base_info: { 
                  aweme_id: awemeId, 
                  desc: "Not Available (API Error)"
                },
                author_info: {},
                location_info: {},
                music_info: {},
                media_info: {}
              };
              await saveDouyinToDatabase(minimalData);
            }
            
            // 返回标准格式的响应，包含aweme_id和not_available标记
            return { 
              data: { aweme_id: awemeId },
              not_available: true,
              status_message: "长视频内容不支持下载",
              is_long_video: true,
              silent_process: true  // 添加静默处理标记
            };
          } else {
            logger.warning(`[callHybridVideoData] TikHub API未返回有效的aweme_id`);
          }
        } catch (backupError) {
          logger.error(`[callHybridVideoData] 备用API调用失败: ${backupError.message}`, backupError);
        }
        
        // 如果备用方案也失败，则继续原有的逻辑
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const jsonData = JSON.parse(text);
      const isNotAvailable = (
        !jsonData 
        || !jsonData.data 
        || jsonData.status_code === 404
        || jsonData.status_code === 10000
        || (jsonData.data && jsonData.data.status_code === 404)
        || (jsonData.data && jsonData.data.error_code === 10000)
        || (jsonData.data && jsonData.data.aweme_id === undefined)
        || (text.includes("Not Found") && text.includes("error"))
        || text.includes("作品不存在")
        || text.includes("视频不见了")
      );
      
      if (isNotAvailable) {
        let awemeId = null;
        const videoIdMatch = douyinUrl.match(/\/video\/(\d+)/);
        const noteIdMatch = douyinUrl.match(/\/note\/(\d+)/);
        const lvDetailMatch = douyinUrl.match(/\/lvdetail\/(\d+)/);
        
        if (videoIdMatch && videoIdMatch[1]) {
          awemeId = videoIdMatch[1];
        } else if (noteIdMatch && noteIdMatch[1]) {
          awemeId = noteIdMatch[1];
        } else if (lvDetailMatch && lvDetailMatch[1]) {
          // 处理长视频链接格式
          awemeId = lvDetailMatch[1];
          logger.info(`[callHybridVideoData] 检测到长视频格式，ID: ${awemeId}`);
        }
        
        if (awemeId) {
          logger.warning(`检测到不可用作品: ${awemeId}，将标记为not available`);
          const minimalData = {
            base_info: { aweme_id: awemeId, desc: "Not Available" },
            author_info: {},
            location_info: {},
            music_info: {},
            media_info: {}
          };
          await saveDouyinToDatabase(minimalData);
          return { 
            data: { aweme_id: awemeId },
            not_available: true,
            status_message: "作品不可用或已删除"
          };
        }
      }
      
      // 将视频时长信息添加到返回数据中，但不在这里处理
      if (jsonData && jsonData.data && jsonData.data.duration) {
        const videoDuration = parseInt(jsonData.data.duration) || 0;
        const videoDurationSeconds = videoDuration / 1000;
        logger.info(`[callHybridVideoData] API返回的视频时长: ${videoDurationSeconds.toFixed(2)}秒 (原始值: ${videoDuration}毫秒)`);
        
        // 只是将时长信息添加到返回数据中，让主流程决定如何处理
        jsonData.video_duration_seconds = videoDurationSeconds;
        jsonData.video_duration_ms = videoDuration;
      }
      
      logger.info(`[callHybridVideoData] 完成视频检测，返回响应数据`);
      return jsonData;
    } catch (error) {
      logger.error(`获取视频信息失败: ${error.message}`, error);
      throw error;
    }
  }, 3, 2000);
}

// =============== 主入口 ===============
async function main() {
  logger.info("Starting douyin_bot...");
  
  // 显示直播按钮状态
  if (SHOW_LIVE_BUTTONS) {
    logger.info("直播订阅按钮已启用 (使用了 'live' 参数)");
  } else {
    logger.info("直播订阅按钮已禁用 (启动时添加 'live' 参数以启用)");
  }
  
  if (!fsSync.existsSync('douyin')) {
    fsSync.mkdirSync('douyin');
  }
  
  logger.info("Launching bot...");
  logger.info(`Bot token 存在: ${!!process.env.BUTHISBOT}`);
  logger.info(`Bot token 长度: ${process.env.BUTHISBOT?.length || 0}`);
  
  console.log("=== 准备调用 bot.launch() ===");
  
  try {
    // 使用 startPolling 而不是 launch
    console.log("=== 使用 startPolling 启动 bot ===");
    
    // 获取 bot 信息以确认连接
    const me = await bot.telegram.getMe();
    console.log(`Bot 连接成功: @${me.username}`);
    
    // 手动启动轮询
    bot.startPolling();
    
    console.log("=== Bot 轮询已启动 ===");
    
    logger.info("Bot is running!");
    console.log("=== Bot launch completed ===");
    
    try {
      const me = await bot.telegram.getMe();
      logger.info(`Logged in as: ${me.username} (ID: ${me.id})`);
    } catch (err) {
      logger.error(`Failed to get bot info: ${err.message}`, err);
    }
    
    // Worker系统已禁用
    // try {
    //   await initializeWorkerSystem(bot);
    //   logger.info("Worker系统初始化成功");
    // } catch (workerError) {
    //   logger.error(`Worker系统初始化失败: ${workerError.message}`, workerError);
    //   logger.warning("Bot将以单线程模式运行");
    // }
    
    logger.info("开始初始化Realtime监听...");
    realtimeChannel = await initRealtimeListener(bot);
    if (realtimeChannel) {
      logger.info("Realtime监听已激活");
      // 添加测试日志
      logger.info(`Realtime channel状态: ${realtimeChannel.state}`);
    } else {
      logger.warning("Realtime监听初始化失败");
    }
    
    // 设置直播内容 Realtime 监听
    try {
      await setupLiveContentRealtimeListeners();
      logger.info("直播内容 Realtime 监听设置成功");
    } catch (liveRealtimeError) {
      logger.error(`直播内容 Realtime 监听设置失败: ${liveRealtimeError.message}`, liveRealtimeError);
    }
    
    logger.info("✅ Bot 已完全启动，等待接收消息...");
    
  } catch (err) {
    logger.error(`Bot 启动失败: ${err.message}`, err);
    console.error('详细错误信息:', err);
    process.exit(1);
  }
  
  process.once('SIGINT', async () => {
    logger.info('收到SIGINT信号，正在关闭...');
    try {
      
      // 停止接收新消息
      bot.stop('SIGINT');
      
      // 关闭 Realtime 监听
      if (realtimeChannel) {
        logger.info('正在关闭Realtime监听...');
        await realtimeChannel.unsubscribe();
      }
      
      // 关闭 Worker 池
      if (workerPool) {
        logger.info('正在关闭Worker池...');
        await workerPool.shutdown();
      }
      
      // 关闭任务队列
      if (taskQueue) {
        logger.info('正在关闭任务队列...');
        await taskQueue.close();
      }
      
      // 关闭 Telegram 队列
      if (telegramQueue) {
        logger.info('正在关闭Telegram队列...');
        // telegramQueue 可能需要自己的清理方法
      }
      
      logger.info('程序已安全退出');
      process.exit(0);
    } catch (error) {
      logger.error('关闭时出错:', error);
      process.exit(1);
    }
  });
  
  process.once('SIGTERM', async () => {
    logger.info('收到SIGTERM信号，正在关闭...');
    try {
      
      bot.stop('SIGTERM');
      
      if (realtimeChannel) {
        await realtimeChannel.unsubscribe();
      }
      
      if (workerPool) {
        await workerPool.shutdown();
      }
      
      if (taskQueue) {
        await taskQueue.close();
      }
      
      process.exit(0);
    } catch (error) {
      logger.error('关闭时出错:', error);
      process.exit(1);
    }
  });
}

main();

// 全局异常捕获
process.on('uncaughtException', (error) => {
  try {
    logger.error(`全局未捕获异常: ${error.message}`, error);
    logger.error(`堆栈: ${error.stack}`);
  } catch (logError) {
    console.error('记录错误日志时出错:', logError);
    console.error('原始错误:', error);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  try {
    logger.error(`未处理的Promise拒绝: ${reason instanceof Error ? reason.message : String(reason)}`);
    if (reason instanceof Error) {
      logger.error(`堆栈: ${reason.stack}`);
    }
  } catch (logError) {
    console.error('记录错误日志时出错:', logError);
    console.error('原始拒绝原因:', reason);
  }
});

// 在 numberWithCommas 函数后新增两个辅助函数，用于检测作品和直播订阅状态
// ... existing code ...
async function isUserSubscribedWork(chatId, uid) {
  try {
    if (!chatId || !uid) return false;
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .eq("douyin", true)  // 仅检查作品订阅
      .limit(1);
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅作品 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    return count > 0;
  } catch (err) {
    logger.error(`isUserSubscribedWork 异常: ${err.message}`, err);
    return false;
  }
}

async function isUserSubscribedLive(chatId, uid) {
  try {
    if (!chatId || !uid) return false;
    const { count, error } = await supabase
      .from("telegram_douyin_map")
      .select("*", { count: 'exact', head: true })
      .eq("user_id", chatId)
      .eq("uid", uid)
      .eq("live", true)  // 仅检查直播订阅
      .limit(1);
    if (error) {
      logger.error(`检查用户 ${chatId} 是否订阅直播 ${uid} 失败: ${error.message}`, error);
      return false;
    }
    return count > 0;
  } catch (err) {
    logger.error(`isUserSubscribedLive 异常: ${err.message}`, err);
    return false;
  }
}

// =============== 日志记录函数 ===============
async function appendToDoesLog(logMessage) {
  try {
    // 首先获取当前的log内容
    const { data: doesData, error: selectError } = await supabase
      .from('does')
      .select('log')
      .limit(1)
      .single();
    
    let currentLog = '';
    if (selectError) {
      // 如果没有记录，创建一条
      if (selectError.code === 'PGRST116') {
        const { error: insertError } = await supabase
          .from('does')
          .insert({ log: logMessage });
        
        if (insertError) {
          logger.error(`创建does日志记录失败: ${insertError.message}`);
        }
        return;
      } else {
        logger.error(`查询does表失败: ${selectError.message}`);
        return;
      }
    }
    
    currentLog = doesData?.log || '';
    
    // 追加新日志
    const updatedLog = currentLog + '\n' + logMessage;
    
    // 更新log字段
    const { error: updateError } = await supabase
      .from('does')
      .update({ log: updatedLog })
      .eq('id', doesData.id);
    
    if (updateError) {
      logger.error(`更新does日志失败: ${updateError.message}`);
    }
  } catch (error) {
    logger.error(`appendToDoesLog异常: ${error.message}`, error);
  }
}

// =============== Realtime 监听直播内容变化 ===============
async function setupLiveContentRealtimeListeners() {
  logger.info("设置直播内容 Realtime 监听...");
  
  // 监听 file_id 表的变化
  const fileIdChannel = supabase
    .channel('file_id_changes')
    .on(
      'postgres_changes',
      { 
        event: 'UPDATE', 
        schema: 'public', 
        table: 'file_id'
      },
      async (payload) => {
        try {
          const { new: newRecord, old: oldRecord } = payload;
          
          // 检查是否是从 "pending" 变为实际的文件ID
          if (oldRecord.file_id === 'pending' && newRecord.file_id && newRecord.file_id !== 'pending') {
            // 判断文件类型
            let fileType = null;
            if (newRecord.mp4 === true) {
              fileType = 'mp4';
            } else if (newRecord.ts === true) {
              fileType = 'ts';
            } else {
              logger.warn(`无法确定文件类型: mp4=${newRecord.mp4}, ts=${newRecord.ts}`);
              return;
            }
            
            // 记录初始日志
            const timestamp = new Date().toISOString();
            const initialLog = `[${timestamp}] 检测到file_id变化: pending -> ${newRecord.file_id}\n` +
                             `anchor_id: ${newRecord.anchor_id}\n` +
                             `file_type: ${fileType}\n` +
                             `duration: ${newRecord.duration || '未知'}\n`;
            
            await appendToDoesLog(initialLog);
            
            logger.info(`检测到新的${fileType.toUpperCase()}文件: anchor_id=${newRecord.anchor_id}, file_id=${newRecord.file_id}`);
            await handleNewLiveContent(newRecord.anchor_id, newRecord.file_id, fileType, newRecord);
          }
        } catch (error) {
          logger.error(`处理file_id变化时出错: ${error.message}`, error);
        }
      }
    )
    .subscribe();

  logger.info("直播内容 Realtime 监听已设置");
}

// 更新直播内容发送状态
async function updateLiveContentSentStatus(anchorId, fileId, sentStatus) {
  if (!anchorId || !fileId) {
    logger.error(`无法更新直播内容发送状态：缺少 anchorId 或 fileId`);
    return;
  }
  
  try {
    // 更新 file_id 表的 sent 字段
    const { error: updateError } = await supabase
      .from("file_id")
      .update({ sent: sentStatus })
      .eq("anchor_id", anchorId)
      .eq("file_id", fileId);
    
    if (updateError) {
      logger.error(`更新 file_id 表 sent 字段失败: ${updateError.message}`);
    } else {
      logger.info(`已更新直播内容发送状态: anchorId=${anchorId}, fileId=${fileId}, sent=${sentStatus}`);
    }
    
  } catch (error) {
    logger.error(`更新直播内容发送状态时发生异常: ${error.message}`, error);
  }
}

// 处理新的直播内容
async function handleNewLiveContent(anchorId, fileId, fileType, recordData) {
  try {
    // 1. 通过 anchor_id 获取作者信息
    const { data: authorData, error: authorError } = await supabase
      .from("douyin_user")
      .select("uid, nickname, unique_id")
      .eq("anchor_id", anchorId)
      .limit(1);

    if (authorError || !authorData || authorData.length === 0) {
      logger.error(`找不到anchor_id ${anchorId} 对应的作者`);
      await appendToDoesLog(`[错误] 找不到anchor_id ${anchorId} 对应的作者`);
      await updateLiveContentSentStatus(anchorId, fileId, false);
      return;
    }

    const author = authorData[0];
    logger.info(`找到作者: ${author.nickname} (uid: ${author.uid})`);
    await appendToDoesLog(`找到作者: ${author.nickname} (uid: ${author.uid})`);

    // 2. 获取直播内容的duration (从传入的recordData中获取，或者查询file_id表)
    let duration = recordData?.duration || null;
    if (!duration) {
      const { data: liveData, error: liveError } = await supabase
        .from('file_id')
        .select('duration')
        .eq('anchor_id', anchorId)
        .eq('file_id', fileId)
        .limit(1);
      
      if (!liveError && liveData && liveData.length > 0) {
        duration = liveData[0].duration;
        logger.info(`获取到直播时长: ${duration}`);
      } else {
        logger.warn(`无法获取直播时长: ${liveError ? liveError.message : '数据不存在'}`);
      }
    }

    // 3. 获取订阅了该作者直播的用户
    const { data: subscribers, error: subError } = await supabase
      .from("telegram_douyin_map")
      .select("user_id")
      .eq("uid", author.uid)
      .eq("live", true);

    if (subError) {
      logger.error(`获取订阅者失败: ${subError.message}`);
      return;
    }

    if (!subscribers || subscribers.length === 0) {
      logger.info(`作者 ${author.nickname} 没有直播订阅者`);
      await appendToDoesLog(`作者 ${author.nickname} 没有直播订阅者，退出处理`);
      return;
    }

    logger.info(`作者 ${author.nickname} 有 ${subscribers.length} 个直播订阅者`);
    await appendToDoesLog(`作者 ${author.nickname} 有 ${subscribers.length} 个直播订阅者: ${subscribers.map(s => s.user_id).join(', ')}`);

    // 3. 先测试发送到 STORAGE_CHANNEL_ID
    try {
      logger.info(`先测试发送 ${fileType} 文件到存储频道 (${STORAGE_CHANNEL_ID})`);
      
      if (fileType === 'mp4') {
        await bot.telegram.sendVideo(STORAGE_CHANNEL_ID, fileId);
      } else {
        await bot.telegram.sendDocument(STORAGE_CHANNEL_ID, fileId);
      }
      
      logger.info(`测试发送成功，文件ID有效`);
      await appendToDoesLog(`测试发送到存储频道成功，文件ID有效: ${fileId}`);
    } catch (testError) {
      // 检查是否是文件ID无效的错误
      const isWrongFileIdError = testError.response 
        && testError.response.error_code === 400 
        && testError.response.description 
        && testError.response.description.includes('wrong file identifier');
      
      if (isWrongFileIdError) {
        logger.error(`测试发送失败 - 文件ID无效: ${testError.message}`);
        await appendToDoesLog(`[错误] 测试发送失败 - 文件ID无效: ${fileId}`);
        // 标记发送失败状态并退出
        await updateLiveContentSentStatus(anchorId, fileId, false);
        return;
      } else {
        logger.error(`测试发送失败 - 其他错误: ${testError.message}`);
        await appendToDoesLog(`[错误] 测试发送失败 - 其他错误: ${testError.message}`);
        // 其他错误也标记发送失败并退出
        await updateLiveContentSentStatus(anchorId, fileId, false);
        return;
      }
    }

    // 4. 构建消息
    let caption = `🔴 <b>${author.nickname}</b> 的直播回放\n` +
                  `📅 时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n`;
    
    // 添加时长信息
    if (duration) {
      // 格式化时长显示
      const formatDuration = (intervalStr) => {
        if (!intervalStr) return '未知';
        
        // 处理PostgreSQL interval格式，如 "01:23:45" 或 "1:23:45"
        const parts = intervalStr.split(':');
        if (parts.length === 3) {
          const hours = parseInt(parts[0]) || 0;
          const minutes = parseInt(parts[1]) || 0;
          const seconds = parseInt(parts[2]) || 0;
          
          let result = [];
          if (hours > 0) result.push(`${hours}小时`);
          if (minutes > 0) result.push(`${minutes}分钟`);
          if (seconds > 0) result.push(`${seconds}秒`);
          
          return result.join('') || '0秒';
        }
        return intervalStr;
      };
      
      caption += `⏱ 时长: ${formatDuration(duration)}\n`;
    }
    
    caption += `📺 格式: ${fileType.toUpperCase()}`;

    // 构建内联键盘
    const keyboard = Markup.inlineKeyboard([
      Markup.button.callback("取消订阅直播", `unsub_live:${author.uid}`)
    ]);

    // 5. 发送给所有订阅者
    let successCount = 0;
    let failedCount = 0;

    for (const subscriber of subscribers) {
      try {
        const userId = subscriber.user_id;
        
        if (fileType === 'mp4') {
          await bot.telegram.sendVideo(userId, fileId, {
            caption: caption,
            reply_markup: keyboard.reply_markup,
            parse_mode: 'HTML'
          });
        } else {
          // TS文件作为document发送
          await bot.telegram.sendDocument(userId, fileId, {
            caption: caption,
            reply_markup: keyboard.reply_markup,
            parse_mode: 'HTML'
          });
        }
        
        successCount++;
        logger.info(`成功向用户 ${userId} 发送 ${fileType} 文件`);
        await appendToDoesLog(`[成功] 向用户 ${userId} 发送 ${fileType} 文件`);
        
        // 更新用户的 douyin_live 累计时长
        if (duration) {
          try {
            // 使用 Supabase 的 SQL 查询来直接累加 interval
            const { data, error: updateError } = await supabase
              .from('users2')
              .update({ 
                douyin_live: supabase.sql`COALESCE(douyin_live, '00:00:00'::interval) + ${duration}::interval`
              })
              .eq('user_id', userId);
            
            if (updateError) {
              logger.error(`更新用户 ${userId} 的 douyin_live 失败: ${updateError.message}`);
            } else {
              logger.info(`成功更新用户 ${userId} 的 douyin_live 时长: ${duration}`);
            }
          } catch (err) {
            logger.error(`更新用户 ${userId} 的 douyin_live 时长失败: ${err.message}`);
          }
        }
        
        // 避免发送过快
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        failedCount++;
        logger.error(`向用户 ${subscriber.user_id} 发送直播内容失败: ${error.message}`);
        await appendToDoesLog(`[失败] 向用户 ${subscriber.user_id} 发送失败: ${error.message}`);
        // 由于已经在测试阶段验证了文件ID，这里的失败通常是用户相关的问题（如用户屏蔽了bot）
      }
    }

    logger.info(`直播内容推送完成 - 成功: ${successCount}, 失败: ${failedCount}`);
    await appendToDoesLog(`\n发送完成 - 成功: ${successCount}, 失败: ${failedCount}`);
    
    // 根据发送结果更新状态
    if (successCount > 0) {
      // 至少有一个订阅者成功接收，标记为已发送
      await updateLiveContentSentStatus(anchorId, fileId, true);
      await appendToDoesLog(`更新 file_id 表 sent=true`);
    } else if (failedCount > 0) {
      // 所有订阅者都发送失败，标记为发送失败
      await updateLiveContentSentStatus(anchorId, fileId, false);
      await appendToDoesLog(`更新 file_id 表 sent=false`);
    }
    
  } catch (error) {
    logger.error(`处理新直播内容时出错: ${error.message}`, error);
    await appendToDoesLog(`[错误] 处理新直播内容时出错: ${error.message}`);
    await updateLiveContentSentStatus(anchorId, fileId, false);
  }
}

// ... existing code ...
