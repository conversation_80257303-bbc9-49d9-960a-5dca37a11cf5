{"permissions": {"allow": ["Bash(grep:*)", "Bash(cp:*)", "Bash(find:*)", "<PERSON><PERSON>(test -f .env)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(timeout:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(rm:*)", "Bash(rg:*)", "<PERSON><PERSON>(node test-bot.js)", "<PERSON><PERSON>(curl:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(echo:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B5 -A5 \"\\.large|\"\"large\"\"|''large''\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B10 -A10 \"large.*http|large.*url\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B20 -A20 \"large_tasks_check.*=.*create_task.*check_and_get_large_tasks\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -A30 \"获取 large 任务检查结果\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"点赞|liked.*video|is_liked\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B10 -A10 \"is_liked_video=True\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B20 -A20 \"fetch.*liked|get.*liked\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B5 -A20 \"def.*collect_liked\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"def.*saved|def.*收藏\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B10 -A10 \"saved_by.*hulu|saved_by.*500\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B30 -A5 \"请求点赞数据失败.*第.*页\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B50 \"仅获取第一页点赞数据\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B10 \"开始获取.*的点赞视频ID列表\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B20 \"video_ids = \\[\\]\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"page_count < 1.*仅第一页\\|is_full_scan or page_count\" surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"page_count < 1\" surveillance.py)", "Bash(timedatectl:*)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"async def.*aweme_id\" /home/<USER>/DLR/DLR/surveillance.py)", "Bash(dmesg:*)", "<PERSON>sh(sudo dmesg:*)", "<PERSON><PERSON>(top:*)", "<PERSON><PERSON>(ulimit:*)", "<PERSON><PERSON>(journalctl:*)", "<PERSON><PERSON>(screen:*)", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "<PERSON><PERSON>(python3:*)", "Bash(python test_fetch_posts.py:*)", "mcp__supabase__apply_migration", "<PERSON><PERSON>(python:*)", "Bash(pip3 list:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(sox:*)", "Bash(dpkg:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(ffmpeg:*)", "Bash(pip install:*)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"except Exception.*媒体发送失败\" /home/<USER>/DLR/DLR/surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -A50 \"except Exception as e:.*整体媒体发送失败|logger.error.*媒体发送失败，跳过数据库更新\" /home/<USER>/DLR/DLR/surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"except Exception as e:\" /home/<USER>/DLR/DLR/surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B5 -A20 \"except Exception as e:.*整体媒体发送失败\" /home/<USER>/DLR/DLR/surveillance.py)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -B20 -A5 \"整体媒体发送失败，跳过数据库更新\" /home/<USER>/DLR/DLR/surveillance.py)"], "deny": []}, "enableAllProjectMcpServers": false}